{"name": "@my-org/server-core", "private": true, "version": "0.0.1", "type": "module", "exports": {".": {"types": "./dist/index.d.ts", "default": "./src/index.ts"}}, "scripts": {"build": "tsc", "dev": "tsc --watch", "check-types": "tsc --noEmit", "test": "vitest", "test:run": "vitest run", "test:coverage": "vitest run --coverage"}, "dependencies": {"@coozf/db": "workspace:*", "@trpc/server": "11.4.2", "drizzle-orm": "^0.44.2", "zod": "catalog:", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "ioredis": "^5.6.1", "nanoid": "^5.1.5", "superjson": "^2.2.1", "lru-cache": "^11.1.0"}, "devDependencies": {"@repo/typescript-config": "workspace:*", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.7", "@vitest/coverage-v8": "^2.1.8", "fastify": "^5.4.0", "typescript": "^5.7.3", "vitest": "^2.1.8"}}