{"/Users/<USER>/code/open-trpc/packages/server-core/vitest.config.ts": {"path": "/Users/<USER>/code/open-trpc/packages/server-core/vitest.config.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 44}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 29}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 9}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 18}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 24}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 15}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 21}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 41}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 16}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 24}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 16}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 20}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 23}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 23}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 8}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 6}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 4}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 2}}}, "s": {"0": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 19, "column": -108}}, "locations": [{"start": {"line": 1, "column": 0}, "end": {"line": 19, "column": -108}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 0}, "end": {"line": 19, "column": -108}}, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 19, "column": -108}}, "line": 1}}, "f": {"0": 0}}, "/Users/<USER>/code/open-trpc/packages/server-core/src/container.ts": {"path": "/Users/<USER>/code/open-trpc/packages/server-core/src/container.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 31}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 24}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 40}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 28}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 30}}, "57": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 30}}, "58": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 38}}, "59": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 38}}, "60": {"start": {"line": 61, "column": 0}, "end": {"line": 61, "column": 26}}, "61": {"start": {"line": 62, "column": 0}, "end": {"line": 62, "column": 6}}, "63": {"start": {"line": 64, "column": 0}, "end": {"line": 64, "column": 22}}, "66": {"start": {"line": 67, "column": 0}, "end": {"line": 67, "column": 59}}, "67": {"start": {"line": 68, "column": 0}, "end": {"line": 68, "column": 73}}, "68": {"start": {"line": 69, "column": 0}, "end": {"line": 69, "column": 65}}, "69": {"start": {"line": 70, "column": 0}, "end": {"line": 70, "column": 61}}, "72": {"start": {"line": 73, "column": 0}, "end": {"line": 73, "column": 44}}, "73": {"start": {"line": 74, "column": 0}, "end": {"line": 74, "column": 54}}, "74": {"start": {"line": 75, "column": 0}, "end": {"line": 75, "column": 66}}, "75": {"start": {"line": 76, "column": 0}, "end": {"line": 76, "column": 86}}, "78": {"start": {"line": 79, "column": 0}, "end": {"line": 79, "column": 39}}, "79": {"start": {"line": 80, "column": 0}, "end": {"line": 80, "column": 26}}, "80": {"start": {"line": 81, "column": 0}, "end": {"line": 81, "column": 31}}, "81": {"start": {"line": 82, "column": 0}, "end": {"line": 82, "column": 21}}, "82": {"start": {"line": 83, "column": 0}, "end": {"line": 83, "column": 5}}, "84": {"start": {"line": 85, "column": 0}, "end": {"line": 85, "column": 39}}, "85": {"start": {"line": 86, "column": 0}, "end": {"line": 86, "column": 26}}, "86": {"start": {"line": 87, "column": 0}, "end": {"line": 87, "column": 22}}, "87": {"start": {"line": 88, "column": 0}, "end": {"line": 88, "column": 5}}, "89": {"start": {"line": 90, "column": 0}, "end": {"line": 90, "column": 53}}, "90": {"start": {"line": 91, "column": 0}, "end": {"line": 91, "column": 33}}, "91": {"start": {"line": 92, "column": 0}, "end": {"line": 92, "column": 24}}, "92": {"start": {"line": 93, "column": 0}, "end": {"line": 93, "column": 5}}, "94": {"start": {"line": 95, "column": 0}, "end": {"line": 95, "column": 45}}, "95": {"start": {"line": 96, "column": 0}, "end": {"line": 96, "column": 28}}, "96": {"start": {"line": 97, "column": 0}, "end": {"line": 97, "column": 5}}, "98": {"start": {"line": 99, "column": 0}, "end": {"line": 99, "column": 41}}, "99": {"start": {"line": 100, "column": 0}, "end": {"line": 100, "column": 27}}, "100": {"start": {"line": 101, "column": 0}, "end": {"line": 101, "column": 26}}, "101": {"start": {"line": 102, "column": 0}, "end": {"line": 102, "column": 24}}, "102": {"start": {"line": 103, "column": 0}, "end": {"line": 103, "column": 5}}, "103": {"start": {"line": 104, "column": 0}, "end": {"line": 104, "column": 3}}, "108": {"start": {"line": 109, "column": 0}, "end": {"line": 109, "column": 32}}, "109": {"start": {"line": 110, "column": 0}, "end": {"line": 110, "column": 27}}, "110": {"start": {"line": 111, "column": 0}, "end": {"line": 111, "column": 3}}, "115": {"start": {"line": 116, "column": 0}, "end": {"line": 116, "column": 32}}, "118": {"start": {"line": 119, "column": 0}, "end": {"line": 119, "column": 6}}, "119": {"start": {"line": 120, "column": 0}, "end": {"line": 120, "column": 21}}, "120": {"start": {"line": 121, "column": 0}, "end": {"line": 121, "column": 19}}, "121": {"start": {"line": 122, "column": 0}, "end": {"line": 122, "column": 22}}, "122": {"start": {"line": 123, "column": 0}, "end": {"line": 123, "column": 5}}, "124": {"start": {"line": 125, "column": 0}, "end": {"line": 125, "column": 9}}, "125": {"start": {"line": 126, "column": 0}, "end": {"line": 126, "column": 29}}, "126": {"start": {"line": 127, "column": 0}, "end": {"line": 127, "column": 26}}, "127": {"start": {"line": 128, "column": 0}, "end": {"line": 128, "column": 21}}, "128": {"start": {"line": 129, "column": 0}, "end": {"line": 129, "column": 56}}, "129": {"start": {"line": 130, "column": 0}, "end": {"line": 130, "column": 5}}, "131": {"start": {"line": 132, "column": 0}, "end": {"line": 132, "column": 9}}, "133": {"start": {"line": 134, "column": 0}, "end": {"line": 134, "column": 49}}, "134": {"start": {"line": 135, "column": 0}, "end": {"line": 135, "column": 30}}, "135": {"start": {"line": 136, "column": 0}, "end": {"line": 136, "column": 8}}, "136": {"start": {"line": 137, "column": 0}, "end": {"line": 137, "column": 29}}, "137": {"start": {"line": 138, "column": 0}, "end": {"line": 138, "column": 21}}, "138": {"start": {"line": 139, "column": 0}, "end": {"line": 139, "column": 59}}, "139": {"start": {"line": 140, "column": 0}, "end": {"line": 140, "column": 5}}, "141": {"start": {"line": 142, "column": 0}, "end": {"line": 142, "column": 18}}, "142": {"start": {"line": 143, "column": 0}, "end": {"line": 143, "column": 3}}, "143": {"start": {"line": 144, "column": 0}, "end": {"line": 144, "column": 1}}, "148": {"start": {"line": 149, "column": 0}, "end": {"line": 149, "column": 69}}, "149": {"start": {"line": 150, "column": 0}, "end": {"line": 150, "column": 30}}, "150": {"start": {"line": 151, "column": 0}, "end": {"line": 151, "column": 1}}}, "s": {"0": 0, "31": 0, "53": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "63": 0, "66": 0, "67": 0, "68": 0, "69": 0, "72": 0, "73": 0, "74": 0, "75": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "84": 0, "85": 0, "86": 0, "87": 0, "89": 0, "90": 0, "91": 0, "92": 0, "94": 0, "95": 0, "96": 0, "98": 0, "99": 0, "100": 0, "101": 0, "102": 0, "103": 0, "108": 0, "109": 0, "110": 0, "115": 0, "118": 0, "119": 0, "120": 0, "121": 0, "122": 0, "124": 0, "125": 0, "126": 0, "127": 0, "128": 0, "129": 0, "131": 0, "133": 0, "134": 0, "135": 0, "136": 0, "137": 0, "138": 0, "139": 0, "141": 0, "142": 0, "143": 0, "148": 0, "149": 0, "150": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 3624}, "end": {"line": 151, "column": 1}}, "locations": [{"start": {"line": 1, "column": 3624}, "end": {"line": 151, "column": 1}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 3624}, "end": {"line": 151, "column": 1}}, "loc": {"start": {"line": 1, "column": 3624}, "end": {"line": 151, "column": 1}}, "line": 1}}, "f": {"0": 0}}, "/Users/<USER>/code/open-trpc/packages/server-core/src/index.ts": {"path": "/Users/<USER>/code/open-trpc/packages/server-core/src/index.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 15}}}, "s": {"0": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 964}, "end": {"line": 42, "column": 26}}, "locations": [{"start": {"line": 1, "column": 964}, "end": {"line": 42, "column": 26}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 964}, "end": {"line": 42, "column": 26}}, "loc": {"start": {"line": 1, "column": 964}, "end": {"line": 42, "column": 26}}, "line": 1}}, "f": {"0": 0}}, "/Users/<USER>/code/open-trpc/packages/server-core/src/api/_app.ts": {"path": "/Users/<USER>/code/open-trpc/packages/server-core/src/api/_app.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 31}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 33}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 19}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 19}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 33}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 25}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 21}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 2}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 37}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 27}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 2}}}, "s": {"0": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "18": 0, "19": 0, "20": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 25, "column": -171}}, "locations": [{"start": {"line": 1, "column": 0}, "end": {"line": 25, "column": -171}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 0}, "end": {"line": 25, "column": -171}}, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 25, "column": -171}}, "line": 1}}, "f": {"0": 0}}, "/Users/<USER>/code/open-trpc/packages/server-core/src/api/context.ts": {"path": "/Users/<USER>/code/open-trpc/packages/server-core/src/api/context.ts", "all": true, "statementMap": {}, "s": {}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 289}, "end": {"line": 11, "column": 1}}, "locations": [{"start": {"line": 1, "column": 289}, "end": {"line": 11, "column": 1}}]}}, "b": {"0": [1]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 289}, "end": {"line": 11, "column": 1}}, "loc": {"start": {"line": 1, "column": 289}, "end": {"line": 11, "column": 1}}, "line": 1}}, "f": {"0": 1}}, "/Users/<USER>/code/open-trpc/packages/server-core/src/api/trpc.ts": {"path": "/Users/<USER>/code/open-trpc/packages/server-core/src/api/trpc.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 50}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 25}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 21}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 11}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 27}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 26}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 40}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 29}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 46}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 68}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 91}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 18}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 24}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 32}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 23}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 18}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 9}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 41}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 26}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 31}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 31}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 26}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 19}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 32}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 26}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 19}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 29}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 26}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 19}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 29}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 26}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 19}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 41}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 26}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 19}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 11}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 18}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 17}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 35}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 23}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 18}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 9}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 16}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 22}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 43}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 21}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 16}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 7}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 14}}, "57": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 17}}, "58": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 15}}, "59": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 24}}, "60": {"start": {"line": 61, "column": 0}, "end": {"line": 61, "column": 19}}, "61": {"start": {"line": 62, "column": 0}, "end": {"line": 62, "column": 43}}, "62": {"start": {"line": 63, "column": 0}, "end": {"line": 63, "column": 37}}, "63": {"start": {"line": 64, "column": 0}, "end": {"line": 64, "column": 21}}, "64": {"start": {"line": 65, "column": 0}, "end": {"line": 65, "column": 10}}, "65": {"start": {"line": 66, "column": 0}, "end": {"line": 66, "column": 7}}, "66": {"start": {"line": 67, "column": 0}, "end": {"line": 67, "column": 6}}, "67": {"start": {"line": 68, "column": 0}, "end": {"line": 68, "column": 4}}, "69": {"start": {"line": 70, "column": 0}, "end": {"line": 70, "column": 30}}, "70": {"start": {"line": 71, "column": 0}, "end": {"line": 71, "column": 42}}}, "s": {"0": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "69": 0, "70": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 71, "column": -33}}, "locations": [{"start": {"line": 1, "column": 0}, "end": {"line": 71, "column": -33}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 0}, "end": {"line": 71, "column": -33}}, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 71, "column": -33}}, "line": 1}}, "f": {"0": 0}}, "/Users/<USER>/code/open-trpc/packages/server-core/src/api/middleware/auth.middleware.ts": {"path": "/Users/<USER>/code/open-trpc/packages/server-core/src/api/middleware/auth.middleware.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 40}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 46}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 12}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 34}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 2}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 69}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 21}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 47}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 57}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 46}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 18}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 21}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 25}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 27}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 32}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 6}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 3}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 7}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 69}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 44}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 16}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 82}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 22}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 29}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 31}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 27}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 10}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 7}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 44}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 41}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 5}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 17}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 12}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 15}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 13}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 8}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 6}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 19}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 25}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 27}}, "57": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 28}}, "58": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 6}}, "59": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 3}}, "60": {"start": {"line": 61, "column": 0}, "end": {"line": 61, "column": 2}}, "62": {"start": {"line": 63, "column": 0}, "end": {"line": 63, "column": 65}}}, "s": {"0": 0, "6": 0, "7": 0, "8": 0, "9": 0, "11": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "26": 0, "28": 0, "31": 0, "33": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "44": 0, "45": 0, "46": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "62": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 1530}, "end": {"line": 63, "column": 65}}, "locations": [{"start": {"line": 1, "column": 1530}, "end": {"line": 63, "column": 65}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 1530}, "end": {"line": 63, "column": 65}}, "loc": {"start": {"line": 1, "column": 1530}, "end": {"line": 63, "column": 65}}, "line": 1}}, "f": {"0": 0}}, "/Users/<USER>/code/open-trpc/packages/server-core/src/api/middleware/open-auth.middleware.ts": {"path": "/Users/<USER>/code/open-trpc/packages/server-core/src/api/middleware/open-auth.middleware.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 40}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 56}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 12}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 30}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 2}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 71}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 57}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 25}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 27}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 45}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 6}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 3}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 35}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 7}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 66}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 57}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 48}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 23}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 78}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 22}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 29}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 31}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 27}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 10}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 7}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 97}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 25}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 29}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 31}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 27}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 10}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 7}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 50}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 29}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 31}}, "57": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 30}}, "58": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 10}}, "59": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 7}}, "62": {"start": {"line": 63, "column": 0}, "end": {"line": 63, "column": 50}}, "64": {"start": {"line": 65, "column": 0}, "end": {"line": 65, "column": 41}}, "65": {"start": {"line": 66, "column": 0}, "end": {"line": 66, "column": 45}}, "66": {"start": {"line": 67, "column": 0}, "end": {"line": 67, "column": 5}}, "68": {"start": {"line": 69, "column": 0}, "end": {"line": 69, "column": 22}}, "69": {"start": {"line": 70, "column": 0}, "end": {"line": 70, "column": 19}}, "70": {"start": {"line": 71, "column": 0}, "end": {"line": 71, "column": 37}}, "71": {"start": {"line": 72, "column": 0}, "end": {"line": 72, "column": 17}}, "72": {"start": {"line": 73, "column": 0}, "end": {"line": 73, "column": 5}}, "73": {"start": {"line": 74, "column": 0}, "end": {"line": 74, "column": 25}}, "74": {"start": {"line": 75, "column": 0}, "end": {"line": 75, "column": 27}}, "75": {"start": {"line": 76, "column": 0}, "end": {"line": 76, "column": 30}}, "76": {"start": {"line": 77, "column": 0}, "end": {"line": 77, "column": 6}}, "77": {"start": {"line": 78, "column": 0}, "end": {"line": 78, "column": 3}}, "78": {"start": {"line": 79, "column": 0}, "end": {"line": 79, "column": 1}}, "80": {"start": {"line": 81, "column": 0}, "end": {"line": 81, "column": 73}}, "81": {"start": {"line": 82, "column": 0}, "end": {"line": 82, "column": 50}}, "83": {"start": {"line": 84, "column": 0}, "end": {"line": 84, "column": 20}}, "84": {"start": {"line": 85, "column": 0}, "end": {"line": 85, "column": 25}}, "85": {"start": {"line": 86, "column": 0}, "end": {"line": 86, "column": 27}}, "86": {"start": {"line": 87, "column": 0}, "end": {"line": 87, "column": 42}}, "87": {"start": {"line": 88, "column": 0}, "end": {"line": 88, "column": 6}}, "88": {"start": {"line": 89, "column": 0}, "end": {"line": 89, "column": 3}}, "90": {"start": {"line": 91, "column": 0}, "end": {"line": 91, "column": 76}}, "92": {"start": {"line": 93, "column": 0}, "end": {"line": 93, "column": 15}}, "93": {"start": {"line": 94, "column": 0}, "end": {"line": 94, "column": 10}}, "94": {"start": {"line": 95, "column": 0}, "end": {"line": 95, "column": 13}}, "95": {"start": {"line": 96, "column": 0}, "end": {"line": 96, "column": 11}}, "96": {"start": {"line": 97, "column": 0}, "end": {"line": 97, "column": 18}}, "97": {"start": {"line": 98, "column": 0}, "end": {"line": 98, "column": 6}}, "98": {"start": {"line": 99, "column": 0}, "end": {"line": 99, "column": 4}}, "99": {"start": {"line": 100, "column": 0}, "end": {"line": 100, "column": 2}}, "101": {"start": {"line": 102, "column": 0}, "end": {"line": 102, "column": 73}}}, "s": {"0": 0, "11": 0, "12": 0, "13": 0, "14": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "25": 0, "27": 0, "28": 0, "29": 0, "32": 0, "34": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "62": 0, "64": 0, "65": 0, "66": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "80": 0, "81": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "90": 0, "92": 0, "93": 0, "94": 0, "95": 0, "96": 0, "97": 0, "98": 0, "99": 0, "101": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 102, "column": -138}}, "locations": [{"start": {"line": 1, "column": 0}, "end": {"line": 102, "column": -138}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 0}, "end": {"line": 102, "column": -138}}, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 102, "column": -138}}, "line": 1}}, "f": {"0": 0}}, "/Users/<USER>/code/open-trpc/packages/server-core/src/api/routers/application.router.ts": {"path": "/Users/<USER>/code/open-trpc/packages/server-core/src/api/routers/application.router.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 23}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 42}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 65}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 64}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 61}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 2}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 42}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 36}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 76}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 64}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 61}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 64}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 2}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 38}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 36}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 2}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 41}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 28}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 35}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 41}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 22}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 29}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 31}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 27}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 10}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 7}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 11}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 80}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 22}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 21}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 28}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 26}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 9}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 16}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 28}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 42}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 32}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 9}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 23}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 84}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 31}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 42}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 36}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 12}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 9}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 29}}, "57": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 40}}, "58": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 69}}, "59": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 10}}, "60": {"start": {"line": 61, "column": 0}, "end": {"line": 61, "column": 7}}, "61": {"start": {"line": 62, "column": 0}, "end": {"line": 62, "column": 7}}, "64": {"start": {"line": 65, "column": 0}, "end": {"line": 65, "column": 26}}, "65": {"start": {"line": 66, "column": 0}, "end": {"line": 66, "column": 31}}, "66": {"start": {"line": 67, "column": 0}, "end": {"line": 67, "column": 22}}, "67": {"start": {"line": 68, "column": 0}, "end": {"line": 68, "column": 29}}, "68": {"start": {"line": 69, "column": 0}, "end": {"line": 69, "column": 31}}, "69": {"start": {"line": 70, "column": 0}, "end": {"line": 70, "column": 27}}, "70": {"start": {"line": 71, "column": 0}, "end": {"line": 71, "column": 10}}, "71": {"start": {"line": 72, "column": 0}, "end": {"line": 72, "column": 7}}, "73": {"start": {"line": 74, "column": 0}, "end": {"line": 74, "column": 11}}, "74": {"start": {"line": 75, "column": 0}, "end": {"line": 75, "column": 100}}, "75": {"start": {"line": 76, "column": 0}, "end": {"line": 76, "column": 27}}, "76": {"start": {"line": 77, "column": 0}, "end": {"line": 77, "column": 23}}, "77": {"start": {"line": 78, "column": 0}, "end": {"line": 78, "column": 29}}, "78": {"start": {"line": 79, "column": 0}, "end": {"line": 79, "column": 40}}, "79": {"start": {"line": 80, "column": 0}, "end": {"line": 80, "column": 71}}, "80": {"start": {"line": 81, "column": 0}, "end": {"line": 81, "column": 10}}, "81": {"start": {"line": 82, "column": 0}, "end": {"line": 82, "column": 7}}, "82": {"start": {"line": 83, "column": 0}, "end": {"line": 83, "column": 7}}, "85": {"start": {"line": 86, "column": 0}, "end": {"line": 86, "column": 28}}, "86": {"start": {"line": 87, "column": 0}, "end": {"line": 87, "column": 31}}, "87": {"start": {"line": 88, "column": 0}, "end": {"line": 88, "column": 38}}, "88": {"start": {"line": 89, "column": 0}, "end": {"line": 89, "column": 22}}, "89": {"start": {"line": 90, "column": 0}, "end": {"line": 90, "column": 29}}, "90": {"start": {"line": 91, "column": 0}, "end": {"line": 91, "column": 31}}, "91": {"start": {"line": 92, "column": 0}, "end": {"line": 92, "column": 27}}, "92": {"start": {"line": 93, "column": 0}, "end": {"line": 93, "column": 10}}, "93": {"start": {"line": 94, "column": 0}, "end": {"line": 94, "column": 7}}, "95": {"start": {"line": 96, "column": 0}, "end": {"line": 96, "column": 11}}, "96": {"start": {"line": 97, "column": 0}, "end": {"line": 97, "column": 95}}, "98": {"start": {"line": 99, "column": 0}, "end": {"line": 99, "column": 27}}, "99": {"start": {"line": 100, "column": 0}, "end": {"line": 100, "column": 31}}, "100": {"start": {"line": 101, "column": 0}, "end": {"line": 101, "column": 30}}, "101": {"start": {"line": 102, "column": 0}, "end": {"line": 102, "column": 29}}, "102": {"start": {"line": 103, "column": 0}, "end": {"line": 103, "column": 12}}, "103": {"start": {"line": 104, "column": 0}, "end": {"line": 104, "column": 9}}, "106": {"start": {"line": 107, "column": 0}, "end": {"line": 107, "column": 49}}, "107": {"start": {"line": 108, "column": 0}, "end": {"line": 108, "column": 31}}, "108": {"start": {"line": 109, "column": 0}, "end": {"line": 109, "column": 30}}, "109": {"start": {"line": 110, "column": 0}, "end": {"line": 110, "column": 31}}, "110": {"start": {"line": 111, "column": 0}, "end": {"line": 111, "column": 12}}, "111": {"start": {"line": 112, "column": 0}, "end": {"line": 112, "column": 9}}, "113": {"start": {"line": 114, "column": 0}, "end": {"line": 114, "column": 26}}, "114": {"start": {"line": 115, "column": 0}, "end": {"line": 115, "column": 23}}, "115": {"start": {"line": 116, "column": 0}, "end": {"line": 116, "column": 41}}, "116": {"start": {"line": 117, "column": 0}, "end": {"line": 117, "column": 21}}, "117": {"start": {"line": 118, "column": 0}, "end": {"line": 118, "column": 9}}, "119": {"start": {"line": 120, "column": 0}, "end": {"line": 120, "column": 29}}, "120": {"start": {"line": 121, "column": 0}, "end": {"line": 121, "column": 40}}, "121": {"start": {"line": 122, "column": 0}, "end": {"line": 122, "column": 71}}, "122": {"start": {"line": 123, "column": 0}, "end": {"line": 123, "column": 10}}, "123": {"start": {"line": 124, "column": 0}, "end": {"line": 124, "column": 7}}, "124": {"start": {"line": 125, "column": 0}, "end": {"line": 125, "column": 7}}, "127": {"start": {"line": 128, "column": 0}, "end": {"line": 128, "column": 28}}, "128": {"start": {"line": 129, "column": 0}, "end": {"line": 129, "column": 35}}, "129": {"start": {"line": 130, "column": 0}, "end": {"line": 130, "column": 41}}, "130": {"start": {"line": 131, "column": 0}, "end": {"line": 131, "column": 22}}, "131": {"start": {"line": 132, "column": 0}, "end": {"line": 132, "column": 29}}, "132": {"start": {"line": 133, "column": 0}, "end": {"line": 133, "column": 31}}, "133": {"start": {"line": 134, "column": 0}, "end": {"line": 134, "column": 27}}, "134": {"start": {"line": 135, "column": 0}, "end": {"line": 135, "column": 10}}, "135": {"start": {"line": 136, "column": 0}, "end": {"line": 136, "column": 7}}, "137": {"start": {"line": 138, "column": 0}, "end": {"line": 138, "column": 11}}, "138": {"start": {"line": 139, "column": 0}, "end": {"line": 139, "column": 40}}, "139": {"start": {"line": 140, "column": 0}, "end": {"line": 140, "column": 92}}, "140": {"start": {"line": 141, "column": 0}, "end": {"line": 141, "column": 13}}, "141": {"start": {"line": 142, "column": 0}, "end": {"line": 142, "column": 22}}, "142": {"start": {"line": 143, "column": 0}, "end": {"line": 143, "column": 17}}, "143": {"start": {"line": 144, "column": 0}, "end": {"line": 144, "column": 9}}, "145": {"start": {"line": 146, "column": 0}, "end": {"line": 146, "column": 34}}, "146": {"start": {"line": 147, "column": 0}, "end": {"line": 147, "column": 31}}, "147": {"start": {"line": 148, "column": 0}, "end": {"line": 148, "column": 42}}, "148": {"start": {"line": 149, "column": 0}, "end": {"line": 149, "column": 30}}, "149": {"start": {"line": 150, "column": 0}, "end": {"line": 150, "column": 12}}, "150": {"start": {"line": 151, "column": 0}, "end": {"line": 151, "column": 9}}, "152": {"start": {"line": 153, "column": 0}, "end": {"line": 153, "column": 16}}, "153": {"start": {"line": 154, "column": 0}, "end": {"line": 154, "column": 28}}, "154": {"start": {"line": 155, "column": 0}, "end": {"line": 155, "column": 42}}, "155": {"start": {"line": 156, "column": 0}, "end": {"line": 156, "column": 9}}, "156": {"start": {"line": 157, "column": 0}, "end": {"line": 157, "column": 23}}, "157": {"start": {"line": 158, "column": 0}, "end": {"line": 158, "column": 82}}, "158": {"start": {"line": 159, "column": 0}, "end": {"line": 159, "column": 31}}, "159": {"start": {"line": 160, "column": 0}, "end": {"line": 160, "column": 30}}, "160": {"start": {"line": 161, "column": 0}, "end": {"line": 161, "column": 34}}, "161": {"start": {"line": 162, "column": 0}, "end": {"line": 162, "column": 12}}, "162": {"start": {"line": 163, "column": 0}, "end": {"line": 163, "column": 9}}, "164": {"start": {"line": 165, "column": 0}, "end": {"line": 165, "column": 29}}, "165": {"start": {"line": 166, "column": 0}, "end": {"line": 166, "column": 40}}, "166": {"start": {"line": 167, "column": 0}, "end": {"line": 167, "column": 69}}, "167": {"start": {"line": 168, "column": 0}, "end": {"line": 168, "column": 10}}, "168": {"start": {"line": 169, "column": 0}, "end": {"line": 169, "column": 7}}, "169": {"start": {"line": 170, "column": 0}, "end": {"line": 170, "column": 7}}, "172": {"start": {"line": 173, "column": 0}, "end": {"line": 173, "column": 38}}, "173": {"start": {"line": 174, "column": 0}, "end": {"line": 174, "column": 31}}, "174": {"start": {"line": 175, "column": 0}, "end": {"line": 175, "column": 41}}, "175": {"start": {"line": 176, "column": 0}, "end": {"line": 176, "column": 22}}, "176": {"start": {"line": 177, "column": 0}, "end": {"line": 177, "column": 29}}, "177": {"start": {"line": 178, "column": 0}, "end": {"line": 178, "column": 31}}, "178": {"start": {"line": 179, "column": 0}, "end": {"line": 179, "column": 27}}, "179": {"start": {"line": 180, "column": 0}, "end": {"line": 180, "column": 10}}, "180": {"start": {"line": 181, "column": 0}, "end": {"line": 181, "column": 7}}, "182": {"start": {"line": 183, "column": 0}, "end": {"line": 183, "column": 11}}, "183": {"start": {"line": 184, "column": 0}, "end": {"line": 184, "column": 79}}, "184": {"start": {"line": 185, "column": 0}, "end": {"line": 185, "column": 19}}, "185": {"start": {"line": 186, "column": 0}, "end": {"line": 186, "column": 21}}, "186": {"start": {"line": 187, "column": 0}, "end": {"line": 187, "column": 9}}, "188": {"start": {"line": 189, "column": 0}, "end": {"line": 189, "column": 16}}, "189": {"start": {"line": 190, "column": 0}, "end": {"line": 190, "column": 30}}, "190": {"start": {"line": 191, "column": 0}, "end": {"line": 191, "column": 42}}, "191": {"start": {"line": 192, "column": 0}, "end": {"line": 192, "column": 32}}, "192": {"start": {"line": 193, "column": 0}, "end": {"line": 193, "column": 9}}, "193": {"start": {"line": 194, "column": 0}, "end": {"line": 194, "column": 23}}, "194": {"start": {"line": 195, "column": 0}, "end": {"line": 195, "column": 37}}, "195": {"start": {"line": 196, "column": 0}, "end": {"line": 196, "column": 58}}, "196": {"start": {"line": 197, "column": 0}, "end": {"line": 197, "column": 33}}, "197": {"start": {"line": 198, "column": 0}, "end": {"line": 198, "column": 32}}, "198": {"start": {"line": 199, "column": 0}, "end": {"line": 199, "column": 36}}, "199": {"start": {"line": 200, "column": 0}, "end": {"line": 200, "column": 14}}, "200": {"start": {"line": 201, "column": 0}, "end": {"line": 201, "column": 11}}, "201": {"start": {"line": 202, "column": 0}, "end": {"line": 202, "column": 63}}, "202": {"start": {"line": 203, "column": 0}, "end": {"line": 203, "column": 33}}, "203": {"start": {"line": 204, "column": 0}, "end": {"line": 204, "column": 44}}, "204": {"start": {"line": 205, "column": 0}, "end": {"line": 205, "column": 34}}, "205": {"start": {"line": 206, "column": 0}, "end": {"line": 206, "column": 14}}, "206": {"start": {"line": 207, "column": 0}, "end": {"line": 207, "column": 11}}, "207": {"start": {"line": 208, "column": 0}, "end": {"line": 208, "column": 9}}, "209": {"start": {"line": 210, "column": 0}, "end": {"line": 210, "column": 29}}, "210": {"start": {"line": 211, "column": 0}, "end": {"line": 211, "column": 40}}, "211": {"start": {"line": 212, "column": 0}, "end": {"line": 212, "column": 71}}, "212": {"start": {"line": 213, "column": 0}, "end": {"line": 213, "column": 10}}, "213": {"start": {"line": 214, "column": 0}, "end": {"line": 214, "column": 7}}, "214": {"start": {"line": 215, "column": 0}, "end": {"line": 215, "column": 7}}, "217": {"start": {"line": 218, "column": 0}, "end": {"line": 218, "column": 28}}, "218": {"start": {"line": 219, "column": 0}, "end": {"line": 219, "column": 31}}, "219": {"start": {"line": 220, "column": 0}, "end": {"line": 220, "column": 41}}, "220": {"start": {"line": 221, "column": 0}, "end": {"line": 221, "column": 22}}, "221": {"start": {"line": 222, "column": 0}, "end": {"line": 222, "column": 29}}, "222": {"start": {"line": 223, "column": 0}, "end": {"line": 223, "column": 31}}, "223": {"start": {"line": 224, "column": 0}, "end": {"line": 224, "column": 27}}, "224": {"start": {"line": 225, "column": 0}, "end": {"line": 225, "column": 10}}, "225": {"start": {"line": 226, "column": 0}, "end": {"line": 226, "column": 7}}, "227": {"start": {"line": 228, "column": 0}, "end": {"line": 228, "column": 11}}, "228": {"start": {"line": 229, "column": 0}, "end": {"line": 229, "column": 81}}, "229": {"start": {"line": 230, "column": 0}, "end": {"line": 230, "column": 19}}, "230": {"start": {"line": 231, "column": 0}, "end": {"line": 231, "column": 21}}, "231": {"start": {"line": 232, "column": 0}, "end": {"line": 232, "column": 9}}, "233": {"start": {"line": 234, "column": 0}, "end": {"line": 234, "column": 23}}, "234": {"start": {"line": 235, "column": 0}, "end": {"line": 235, "column": 31}}, "235": {"start": {"line": 236, "column": 0}, "end": {"line": 236, "column": 42}}, "236": {"start": {"line": 237, "column": 0}, "end": {"line": 237, "column": 30}}, "237": {"start": {"line": 238, "column": 0}, "end": {"line": 238, "column": 12}}, "238": {"start": {"line": 239, "column": 0}, "end": {"line": 239, "column": 9}}, "240": {"start": {"line": 241, "column": 0}, "end": {"line": 241, "column": 16}}, "241": {"start": {"line": 242, "column": 0}, "end": {"line": 242, "column": 28}}, "242": {"start": {"line": 243, "column": 0}, "end": {"line": 243, "column": 9}}, "243": {"start": {"line": 244, "column": 0}, "end": {"line": 244, "column": 23}}, "244": {"start": {"line": 245, "column": 0}, "end": {"line": 245, "column": 82}}, "245": {"start": {"line": 246, "column": 0}, "end": {"line": 246, "column": 31}}, "246": {"start": {"line": 247, "column": 0}, "end": {"line": 247, "column": 30}}, "247": {"start": {"line": 248, "column": 0}, "end": {"line": 248, "column": 34}}, "248": {"start": {"line": 249, "column": 0}, "end": {"line": 249, "column": 12}}, "249": {"start": {"line": 250, "column": 0}, "end": {"line": 250, "column": 9}}, "251": {"start": {"line": 252, "column": 0}, "end": {"line": 252, "column": 29}}, "252": {"start": {"line": 253, "column": 0}, "end": {"line": 253, "column": 40}}, "253": {"start": {"line": 254, "column": 0}, "end": {"line": 254, "column": 69}}, "254": {"start": {"line": 255, "column": 0}, "end": {"line": 255, "column": 10}}, "255": {"start": {"line": 256, "column": 0}, "end": {"line": 256, "column": 7}}, "256": {"start": {"line": 257, "column": 0}, "end": {"line": 257, "column": 7}}, "257": {"start": {"line": 258, "column": 0}, "end": {"line": 258, "column": 2}}}, "s": {"0": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "19": 0, "20": 0, "21": 0, "23": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "90": 0, "91": 0, "92": 0, "93": 0, "95": 0, "96": 0, "98": 0, "99": 0, "100": 0, "101": 0, "102": 0, "103": 0, "106": 0, "107": 0, "108": 0, "109": 0, "110": 0, "111": 0, "113": 0, "114": 0, "115": 0, "116": 0, "117": 0, "119": 0, "120": 0, "121": 0, "122": 0, "123": 0, "124": 0, "127": 0, "128": 0, "129": 0, "130": 0, "131": 0, "132": 0, "133": 0, "134": 0, "135": 0, "137": 0, "138": 0, "139": 0, "140": 0, "141": 0, "142": 0, "143": 0, "145": 0, "146": 0, "147": 0, "148": 0, "149": 0, "150": 0, "152": 0, "153": 0, "154": 0, "155": 0, "156": 0, "157": 0, "158": 0, "159": 0, "160": 0, "161": 0, "162": 0, "164": 0, "165": 0, "166": 0, "167": 0, "168": 0, "169": 0, "172": 0, "173": 0, "174": 0, "175": 0, "176": 0, "177": 0, "178": 0, "179": 0, "180": 0, "182": 0, "183": 0, "184": 0, "185": 0, "186": 0, "188": 0, "189": 0, "190": 0, "191": 0, "192": 0, "193": 0, "194": 0, "195": 0, "196": 0, "197": 0, "198": 0, "199": 0, "200": 0, "201": 0, "202": 0, "203": 0, "204": 0, "205": 0, "206": 0, "207": 0, "209": 0, "210": 0, "211": 0, "212": 0, "213": 0, "214": 0, "217": 0, "218": 0, "219": 0, "220": 0, "221": 0, "222": 0, "223": 0, "224": 0, "225": 0, "227": 0, "228": 0, "229": 0, "230": 0, "231": 0, "233": 0, "234": 0, "235": 0, "236": 0, "237": 0, "238": 0, "240": 0, "241": 0, "242": 0, "243": 0, "244": 0, "245": 0, "246": 0, "247": 0, "248": 0, "249": 0, "251": 0, "252": 0, "253": 0, "254": 0, "255": 0, "256": 0, "257": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 258, "column": -652}}, "locations": [{"start": {"line": 1, "column": 0}, "end": {"line": 258, "column": -652}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 0}, "end": {"line": 258, "column": -652}}, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 258, "column": -652}}, "line": 1}}, "f": {"0": 0}}, "/Users/<USER>/code/open-trpc/packages/server-core/src/api/routers/auth.router.ts": {"path": "/Users/<USER>/code/open-trpc/packages/server-core/src/api/routers/auth.router.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 23}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 42}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 56}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 41}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 2}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 39}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 56}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 73}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 2}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 37}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 51}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 67}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 40}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 10}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 37}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 3}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 25}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 20}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 3}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 1}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 34}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 26}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 25}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 41}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 11}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 91}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 30}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 31}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 42}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 30}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 12}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 9}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 16}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 29}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 81}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 9}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 23}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 29}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 40}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 67}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 10}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 7}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 7}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 29}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 28}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 41}}, "57": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 11}}, "58": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 80}}, "59": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 22}}, "60": {"start": {"line": 61, "column": 0}, "end": {"line": 61, "column": 20}}, "61": {"start": {"line": 62, "column": 0}, "end": {"line": 62, "column": 9}}, "63": {"start": {"line": 64, "column": 0}, "end": {"line": 64, "column": 16}}, "64": {"start": {"line": 65, "column": 0}, "end": {"line": 65, "column": 54}}, "65": {"start": {"line": 66, "column": 0}, "end": {"line": 66, "column": 30}}, "66": {"start": {"line": 67, "column": 0}, "end": {"line": 67, "column": 38}}, "67": {"start": {"line": 68, "column": 0}, "end": {"line": 68, "column": 9}}, "68": {"start": {"line": 69, "column": 0}, "end": {"line": 69, "column": 23}}, "69": {"start": {"line": 70, "column": 0}, "end": {"line": 70, "column": 29}}, "70": {"start": {"line": 71, "column": 0}, "end": {"line": 71, "column": 30}}, "71": {"start": {"line": 72, "column": 0}, "end": {"line": 72, "column": 67}}, "72": {"start": {"line": 73, "column": 0}, "end": {"line": 73, "column": 10}}, "73": {"start": {"line": 74, "column": 0}, "end": {"line": 74, "column": 7}}, "74": {"start": {"line": 75, "column": 0}, "end": {"line": 75, "column": 7}}, "77": {"start": {"line": 78, "column": 0}, "end": {"line": 78, "column": 24}}, "78": {"start": {"line": 79, "column": 0}, "end": {"line": 79, "column": 23}}, "79": {"start": {"line": 80, "column": 0}, "end": {"line": 80, "column": 41}}, "80": {"start": {"line": 81, "column": 0}, "end": {"line": 81, "column": 11}}, "81": {"start": {"line": 82, "column": 0}, "end": {"line": 82, "column": 61}}, "82": {"start": {"line": 83, "column": 0}, "end": {"line": 83, "column": 73}}, "83": {"start": {"line": 84, "column": 0}, "end": {"line": 84, "column": 23}}, "84": {"start": {"line": 85, "column": 0}, "end": {"line": 85, "column": 24}}, "85": {"start": {"line": 86, "column": 0}, "end": {"line": 86, "column": 9}}, "87": {"start": {"line": 88, "column": 0}, "end": {"line": 88, "column": 16}}, "88": {"start": {"line": 89, "column": 0}, "end": {"line": 89, "column": 26}}, "89": {"start": {"line": 90, "column": 0}, "end": {"line": 90, "column": 30}}, "90": {"start": {"line": 91, "column": 0}, "end": {"line": 91, "column": 9}}, "91": {"start": {"line": 92, "column": 0}, "end": {"line": 92, "column": 23}}, "92": {"start": {"line": 93, "column": 0}, "end": {"line": 93, "column": 29}}, "93": {"start": {"line": 94, "column": 0}, "end": {"line": 94, "column": 30}}, "94": {"start": {"line": 95, "column": 0}, "end": {"line": 95, "column": 67}}, "95": {"start": {"line": 96, "column": 0}, "end": {"line": 96, "column": 10}}, "96": {"start": {"line": 97, "column": 0}, "end": {"line": 97, "column": 7}}, "97": {"start": {"line": 98, "column": 0}, "end": {"line": 98, "column": 7}}, "98": {"start": {"line": 99, "column": 0}, "end": {"line": 99, "column": 2}}}, "s": {"0": 0, "4": 0, "5": 0, "6": 0, "7": 0, "9": 0, "10": 0, "11": 0, "12": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "26": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "87": 0, "88": 0, "89": 0, "90": 0, "91": 0, "92": 0, "93": 0, "94": 0, "95": 0, "96": 0, "97": 0, "98": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 99, "column": -821}}, "locations": [{"start": {"line": 1, "column": 0}, "end": {"line": 99, "column": -821}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 0}, "end": {"line": 99, "column": -821}}, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 99, "column": -821}}, "line": 1}}, "f": {"0": 0}}, "/Users/<USER>/code/open-trpc/packages/server-core/src/api/routers/balance.router.ts": {"path": "/Users/<USER>/code/open-trpc/packages/server-core/src/api/routers/balance.router.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 23}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 38}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 47}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 2}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 33}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 47}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 43}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 2}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 37}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 32}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 31}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 38}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 22}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 29}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 31}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 27}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 10}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 7}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 11}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 106}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 65}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 31}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 30}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 31}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 12}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 9}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 81}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 29}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 9}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 16}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 18}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 45}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 9}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 23}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 41}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 21}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 9}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 29}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 40}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 69}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 10}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 7}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 7}}, "58": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 36}}, "59": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 31}}, "60": {"start": {"line": 61, "column": 0}, "end": {"line": 61, "column": 22}}, "61": {"start": {"line": 62, "column": 0}, "end": {"line": 62, "column": 29}}, "62": {"start": {"line": 63, "column": 0}, "end": {"line": 63, "column": 31}}, "63": {"start": {"line": 64, "column": 0}, "end": {"line": 64, "column": 27}}, "64": {"start": {"line": 65, "column": 0}, "end": {"line": 65, "column": 10}}, "65": {"start": {"line": 66, "column": 0}, "end": {"line": 66, "column": 7}}, "67": {"start": {"line": 68, "column": 0}, "end": {"line": 68, "column": 11}}, "69": {"start": {"line": 70, "column": 0}, "end": {"line": 70, "column": 100}}, "70": {"start": {"line": 71, "column": 0}, "end": {"line": 71, "column": 62}}, "73": {"start": {"line": 74, "column": 0}, "end": {"line": 74, "column": 98}}, "74": {"start": {"line": 75, "column": 0}, "end": {"line": 75, "column": 23}}, "75": {"start": {"line": 76, "column": 0}, "end": {"line": 76, "column": 23}}, "76": {"start": {"line": 77, "column": 0}, "end": {"line": 77, "column": 29}}, "77": {"start": {"line": 78, "column": 0}, "end": {"line": 78, "column": 40}}, "78": {"start": {"line": 79, "column": 0}, "end": {"line": 79, "column": 71}}, "79": {"start": {"line": 80, "column": 0}, "end": {"line": 80, "column": 10}}, "80": {"start": {"line": 81, "column": 0}, "end": {"line": 81, "column": 7}}, "81": {"start": {"line": 82, "column": 0}, "end": {"line": 82, "column": 7}}, "84": {"start": {"line": 85, "column": 0}, "end": {"line": 85, "column": 30}}, "85": {"start": {"line": 86, "column": 0}, "end": {"line": 86, "column": 26}}, "86": {"start": {"line": 87, "column": 0}, "end": {"line": 87, "column": 41}}, "87": {"start": {"line": 88, "column": 0}, "end": {"line": 88, "column": 22}}, "88": {"start": {"line": 89, "column": 0}, "end": {"line": 89, "column": 29}}, "89": {"start": {"line": 90, "column": 0}, "end": {"line": 90, "column": 31}}, "90": {"start": {"line": 91, "column": 0}, "end": {"line": 91, "column": 27}}, "91": {"start": {"line": 92, "column": 0}, "end": {"line": 92, "column": 10}}, "92": {"start": {"line": 93, "column": 0}, "end": {"line": 93, "column": 7}}, "94": {"start": {"line": 95, "column": 0}, "end": {"line": 95, "column": 11}}, "96": {"start": {"line": 97, "column": 0}, "end": {"line": 97, "column": 106}}, "97": {"start": {"line": 98, "column": 0}, "end": {"line": 98, "column": 27}}, "98": {"start": {"line": 99, "column": 0}, "end": {"line": 99, "column": 31}}, "99": {"start": {"line": 100, "column": 0}, "end": {"line": 100, "column": 30}}, "100": {"start": {"line": 101, "column": 0}, "end": {"line": 101, "column": 29}}, "101": {"start": {"line": 102, "column": 0}, "end": {"line": 102, "column": 12}}, "102": {"start": {"line": 103, "column": 0}, "end": {"line": 103, "column": 9}}, "104": {"start": {"line": 105, "column": 0}, "end": {"line": 105, "column": 49}}, "105": {"start": {"line": 106, "column": 0}, "end": {"line": 106, "column": 31}}, "106": {"start": {"line": 107, "column": 0}, "end": {"line": 107, "column": 30}}, "107": {"start": {"line": 108, "column": 0}, "end": {"line": 108, "column": 31}}, "108": {"start": {"line": 109, "column": 0}, "end": {"line": 109, "column": 12}}, "109": {"start": {"line": 110, "column": 0}, "end": {"line": 110, "column": 9}}, "111": {"start": {"line": 112, "column": 0}, "end": {"line": 112, "column": 82}}, "112": {"start": {"line": 113, "column": 0}, "end": {"line": 113, "column": 30}}, "113": {"start": {"line": 114, "column": 0}, "end": {"line": 114, "column": 22}}, "114": {"start": {"line": 115, "column": 0}, "end": {"line": 115, "column": 9}}, "116": {"start": {"line": 117, "column": 0}, "end": {"line": 117, "column": 16}}, "117": {"start": {"line": 118, "column": 0}, "end": {"line": 118, "column": 26}}, "118": {"start": {"line": 119, "column": 0}, "end": {"line": 119, "column": 34}}, "119": {"start": {"line": 120, "column": 0}, "end": {"line": 120, "column": 9}}, "120": {"start": {"line": 121, "column": 0}, "end": {"line": 121, "column": 23}}, "121": {"start": {"line": 122, "column": 0}, "end": {"line": 122, "column": 41}}, "122": {"start": {"line": 123, "column": 0}, "end": {"line": 123, "column": 21}}, "123": {"start": {"line": 124, "column": 0}, "end": {"line": 124, "column": 9}}, "125": {"start": {"line": 126, "column": 0}, "end": {"line": 126, "column": 82}}, "126": {"start": {"line": 127, "column": 0}, "end": {"line": 127, "column": 31}}, "127": {"start": {"line": 128, "column": 0}, "end": {"line": 128, "column": 42}}, "128": {"start": {"line": 129, "column": 0}, "end": {"line": 129, "column": 30}}, "129": {"start": {"line": 130, "column": 0}, "end": {"line": 130, "column": 12}}, "130": {"start": {"line": 131, "column": 0}, "end": {"line": 131, "column": 9}}, "132": {"start": {"line": 133, "column": 0}, "end": {"line": 133, "column": 29}}, "133": {"start": {"line": 134, "column": 0}, "end": {"line": 134, "column": 40}}, "134": {"start": {"line": 135, "column": 0}, "end": {"line": 135, "column": 67}}, "135": {"start": {"line": 136, "column": 0}, "end": {"line": 136, "column": 10}}, "136": {"start": {"line": 137, "column": 0}, "end": {"line": 137, "column": 7}}, "137": {"start": {"line": 138, "column": 0}, "end": {"line": 138, "column": 7}}, "140": {"start": {"line": 141, "column": 0}, "end": {"line": 141, "column": 34}}, "141": {"start": {"line": 142, "column": 0}, "end": {"line": 142, "column": 21}}, "142": {"start": {"line": 143, "column": 0}, "end": {"line": 143, "column": 51}}, "143": {"start": {"line": 144, "column": 0}, "end": {"line": 144, "column": 45}}, "144": {"start": {"line": 145, "column": 0}, "end": {"line": 145, "column": 7}}, "145": {"start": {"line": 146, "column": 0}, "end": {"line": 146, "column": 38}}, "146": {"start": {"line": 147, "column": 0}, "end": {"line": 147, "column": 22}}, "147": {"start": {"line": 148, "column": 0}, "end": {"line": 148, "column": 29}}, "148": {"start": {"line": 149, "column": 0}, "end": {"line": 149, "column": 31}}, "149": {"start": {"line": 150, "column": 0}, "end": {"line": 150, "column": 27}}, "150": {"start": {"line": 151, "column": 0}, "end": {"line": 151, "column": 10}}, "151": {"start": {"line": 152, "column": 0}, "end": {"line": 152, "column": 7}}, "153": {"start": {"line": 154, "column": 0}, "end": {"line": 154, "column": 11}}, "155": {"start": {"line": 156, "column": 0}, "end": {"line": 156, "column": 106}}, "156": {"start": {"line": 157, "column": 0}, "end": {"line": 157, "column": 65}}, "157": {"start": {"line": 158, "column": 0}, "end": {"line": 158, "column": 31}}, "158": {"start": {"line": 159, "column": 0}, "end": {"line": 159, "column": 30}}, "159": {"start": {"line": 160, "column": 0}, "end": {"line": 160, "column": 31}}, "160": {"start": {"line": 161, "column": 0}, "end": {"line": 161, "column": 12}}, "161": {"start": {"line": 162, "column": 0}, "end": {"line": 162, "column": 9}}, "163": {"start": {"line": 164, "column": 0}, "end": {"line": 164, "column": 85}}, "164": {"start": {"line": 165, "column": 0}, "end": {"line": 165, "column": 30}}, "165": {"start": {"line": 166, "column": 0}, "end": {"line": 166, "column": 22}}, "166": {"start": {"line": 167, "column": 0}, "end": {"line": 167, "column": 9}}, "168": {"start": {"line": 169, "column": 0}, "end": {"line": 169, "column": 16}}, "169": {"start": {"line": 170, "column": 0}, "end": {"line": 170, "column": 27}}, "170": {"start": {"line": 171, "column": 0}, "end": {"line": 171, "column": 39}}, "171": {"start": {"line": 172, "column": 0}, "end": {"line": 172, "column": 9}}, "172": {"start": {"line": 173, "column": 0}, "end": {"line": 173, "column": 23}}, "173": {"start": {"line": 174, "column": 0}, "end": {"line": 174, "column": 41}}, "174": {"start": {"line": 175, "column": 0}, "end": {"line": 175, "column": 21}}, "175": {"start": {"line": 176, "column": 0}, "end": {"line": 176, "column": 9}}, "176": {"start": {"line": 177, "column": 0}, "end": {"line": 177, "column": 29}}, "177": {"start": {"line": 178, "column": 0}, "end": {"line": 178, "column": 40}}, "178": {"start": {"line": 179, "column": 0}, "end": {"line": 179, "column": 69}}, "179": {"start": {"line": 180, "column": 0}, "end": {"line": 180, "column": 10}}, "180": {"start": {"line": 181, "column": 0}, "end": {"line": 181, "column": 7}}, "181": {"start": {"line": 182, "column": 0}, "end": {"line": 182, "column": 7}}, "182": {"start": {"line": 183, "column": 0}, "end": {"line": 183, "column": 2}}}, "s": {"0": 0, "5": 0, "6": 0, "7": 0, "9": 0, "10": 0, "11": 0, "12": 0, "16": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "28": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "38": 0, "39": 0, "40": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "67": 0, "69": 0, "70": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "90": 0, "91": 0, "92": 0, "94": 0, "96": 0, "97": 0, "98": 0, "99": 0, "100": 0, "101": 0, "102": 0, "104": 0, "105": 0, "106": 0, "107": 0, "108": 0, "109": 0, "111": 0, "112": 0, "113": 0, "114": 0, "116": 0, "117": 0, "118": 0, "119": 0, "120": 0, "121": 0, "122": 0, "123": 0, "125": 0, "126": 0, "127": 0, "128": 0, "129": 0, "130": 0, "132": 0, "133": 0, "134": 0, "135": 0, "136": 0, "137": 0, "140": 0, "141": 0, "142": 0, "143": 0, "144": 0, "145": 0, "146": 0, "147": 0, "148": 0, "149": 0, "150": 0, "151": 0, "153": 0, "155": 0, "156": 0, "157": 0, "158": 0, "159": 0, "160": 0, "161": 0, "163": 0, "164": 0, "165": 0, "166": 0, "168": 0, "169": 0, "170": 0, "171": 0, "172": 0, "173": 0, "174": 0, "175": 0, "176": 0, "177": 0, "178": 0, "179": 0, "180": 0, "181": 0, "182": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 183, "column": -473}}, "locations": [{"start": {"line": 1, "column": 0}, "end": {"line": 183, "column": -473}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 0}, "end": {"line": 183, "column": -473}}, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 183, "column": -473}}, "line": 1}}, "f": {"0": 0}}, "/Users/<USER>/code/open-trpc/packages/server-core/src/api/routers/index.ts": {"path": "/Users/<USER>/code/open-trpc/packages/server-core/src/api/routers/index.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 42}}}, "s": {"0": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 288}, "end": {"line": 6, "column": 51}}, "locations": [{"start": {"line": 1, "column": 288}, "end": {"line": 6, "column": 51}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 288}, "end": {"line": 6, "column": 51}}, "loc": {"start": {"line": 1, "column": 288}, "end": {"line": 6, "column": 51}}, "line": 1}}, "f": {"0": 0}}, "/Users/<USER>/code/open-trpc/packages/server-core/src/api/routers/open-auth.router.ts": {"path": "/Users/<USER>/code/open-trpc/packages/server-core/src/api/routers/open-auth.router.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 23}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 37}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 78}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 75}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 2}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 45}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 42}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 43}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 2}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 38}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 31}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 30}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 41}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 11}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 91}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 22}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 22}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 9}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 27}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 31}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 33}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 42}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 12}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 9}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 46}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 31}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 30}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 33}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 12}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 9}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 69}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 37}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 35}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 27}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 10}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 16}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 26}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 16}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 24}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 31}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 37}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 35}}, "57": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 39}}, "58": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 12}}, "59": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 9}}, "60": {"start": {"line": 61, "column": 0}, "end": {"line": 61, "column": 23}}, "61": {"start": {"line": 62, "column": 0}, "end": {"line": 62, "column": 41}}, "62": {"start": {"line": 63, "column": 0}, "end": {"line": 63, "column": 21}}, "63": {"start": {"line": 64, "column": 0}, "end": {"line": 64, "column": 9}}, "65": {"start": {"line": 66, "column": 0}, "end": {"line": 66, "column": 29}}, "66": {"start": {"line": 67, "column": 0}, "end": {"line": 67, "column": 40}}, "67": {"start": {"line": 68, "column": 0}, "end": {"line": 68, "column": 67}}, "68": {"start": {"line": 69, "column": 0}, "end": {"line": 69, "column": 10}}, "69": {"start": {"line": 70, "column": 0}, "end": {"line": 70, "column": 7}}, "70": {"start": {"line": 71, "column": 0}, "end": {"line": 71, "column": 7}}, "73": {"start": {"line": 74, "column": 0}, "end": {"line": 74, "column": 39}}, "74": {"start": {"line": 75, "column": 0}, "end": {"line": 75, "column": 38}}, "75": {"start": {"line": 76, "column": 0}, "end": {"line": 76, "column": 41}}, "76": {"start": {"line": 77, "column": 0}, "end": {"line": 77, "column": 11}}, "78": {"start": {"line": 79, "column": 0}, "end": {"line": 79, "column": 91}}, "79": {"start": {"line": 80, "column": 0}, "end": {"line": 80, "column": 22}}, "80": {"start": {"line": 81, "column": 0}, "end": {"line": 81, "column": 22}}, "81": {"start": {"line": 82, "column": 0}, "end": {"line": 82, "column": 9}}, "83": {"start": {"line": 84, "column": 0}, "end": {"line": 84, "column": 27}}, "84": {"start": {"line": 85, "column": 0}, "end": {"line": 85, "column": 31}}, "85": {"start": {"line": 86, "column": 0}, "end": {"line": 86, "column": 33}}, "86": {"start": {"line": 87, "column": 0}, "end": {"line": 87, "column": 42}}, "87": {"start": {"line": 88, "column": 0}, "end": {"line": 88, "column": 12}}, "88": {"start": {"line": 89, "column": 0}, "end": {"line": 89, "column": 9}}, "91": {"start": {"line": 92, "column": 0}, "end": {"line": 92, "column": 46}}, "92": {"start": {"line": 93, "column": 0}, "end": {"line": 93, "column": 31}}, "93": {"start": {"line": 94, "column": 0}, "end": {"line": 94, "column": 30}}, "94": {"start": {"line": 95, "column": 0}, "end": {"line": 95, "column": 33}}, "95": {"start": {"line": 96, "column": 0}, "end": {"line": 96, "column": 12}}, "96": {"start": {"line": 97, "column": 0}, "end": {"line": 97, "column": 9}}, "99": {"start": {"line": 100, "column": 0}, "end": {"line": 100, "column": 90}}, "100": {"start": {"line": 101, "column": 0}, "end": {"line": 101, "column": 29}}, "101": {"start": {"line": 102, "column": 0}, "end": {"line": 102, "column": 24}}, "102": {"start": {"line": 103, "column": 0}, "end": {"line": 103, "column": 9}}, "104": {"start": {"line": 105, "column": 0}, "end": {"line": 105, "column": 16}}, "105": {"start": {"line": 106, "column": 0}, "end": {"line": 106, "column": 40}}, "106": {"start": {"line": 107, "column": 0}, "end": {"line": 107, "column": 23}}, "107": {"start": {"line": 108, "column": 0}, "end": {"line": 108, "column": 33}}, "108": {"start": {"line": 109, "column": 0}, "end": {"line": 109, "column": 9}}, "109": {"start": {"line": 110, "column": 0}, "end": {"line": 110, "column": 23}}, "110": {"start": {"line": 111, "column": 0}, "end": {"line": 111, "column": 41}}, "111": {"start": {"line": 112, "column": 0}, "end": {"line": 112, "column": 21}}, "112": {"start": {"line": 113, "column": 0}, "end": {"line": 113, "column": 9}}, "114": {"start": {"line": 115, "column": 0}, "end": {"line": 115, "column": 29}}, "115": {"start": {"line": 116, "column": 0}, "end": {"line": 116, "column": 40}}, "116": {"start": {"line": 117, "column": 0}, "end": {"line": 117, "column": 81}}, "117": {"start": {"line": 118, "column": 0}, "end": {"line": 118, "column": 10}}, "118": {"start": {"line": 119, "column": 0}, "end": {"line": 119, "column": 7}}, "119": {"start": {"line": 120, "column": 0}, "end": {"line": 120, "column": 7}}, "122": {"start": {"line": 123, "column": 0}, "end": {"line": 123, "column": 37}}, "123": {"start": {"line": 124, "column": 0}, "end": {"line": 124, "column": 21}}, "124": {"start": {"line": 125, "column": 0}, "end": {"line": 125, "column": 60}}, "125": {"start": {"line": 126, "column": 0}, "end": {"line": 126, "column": 7}}, "126": {"start": {"line": 127, "column": 0}, "end": {"line": 127, "column": 38}}, "127": {"start": {"line": 128, "column": 0}, "end": {"line": 128, "column": 11}}, "128": {"start": {"line": 129, "column": 0}, "end": {"line": 129, "column": 85}}, "129": {"start": {"line": 130, "column": 0}, "end": {"line": 130, "column": 28}}, "130": {"start": {"line": 131, "column": 0}, "end": {"line": 131, "column": 9}}, "132": {"start": {"line": 133, "column": 0}, "end": {"line": 133, "column": 25}}, "133": {"start": {"line": 134, "column": 0}, "end": {"line": 134, "column": 31}}, "134": {"start": {"line": 135, "column": 0}, "end": {"line": 135, "column": 33}}, "135": {"start": {"line": 136, "column": 0}, "end": {"line": 136, "column": 44}}, "136": {"start": {"line": 137, "column": 0}, "end": {"line": 137, "column": 12}}, "137": {"start": {"line": 138, "column": 0}, "end": {"line": 138, "column": 9}}, "140": {"start": {"line": 141, "column": 0}, "end": {"line": 141, "column": 82}}, "141": {"start": {"line": 142, "column": 0}, "end": {"line": 142, "column": 20}}, "142": {"start": {"line": 143, "column": 0}, "end": {"line": 143, "column": 31}}, "143": {"start": {"line": 144, "column": 0}, "end": {"line": 144, "column": 33}}, "144": {"start": {"line": 145, "column": 0}, "end": {"line": 145, "column": 29}}, "145": {"start": {"line": 146, "column": 0}, "end": {"line": 146, "column": 12}}, "146": {"start": {"line": 147, "column": 0}, "end": {"line": 147, "column": 9}}, "149": {"start": {"line": 150, "column": 0}, "end": {"line": 150, "column": 86}}, "150": {"start": {"line": 151, "column": 0}, "end": {"line": 151, "column": 33}}, "151": {"start": {"line": 152, "column": 0}, "end": {"line": 152, "column": 9}}, "152": {"start": {"line": 153, "column": 0}, "end": {"line": 153, "column": 27}}, "153": {"start": {"line": 154, "column": 0}, "end": {"line": 154, "column": 31}}, "154": {"start": {"line": 155, "column": 0}, "end": {"line": 155, "column": 33}}, "155": {"start": {"line": 156, "column": 0}, "end": {"line": 156, "column": 29}}, "156": {"start": {"line": 157, "column": 0}, "end": {"line": 157, "column": 12}}, "157": {"start": {"line": 158, "column": 0}, "end": {"line": 158, "column": 9}}, "159": {"start": {"line": 160, "column": 0}, "end": {"line": 160, "column": 16}}, "160": {"start": {"line": 161, "column": 0}, "end": {"line": 161, "column": 22}}, "161": {"start": {"line": 162, "column": 0}, "end": {"line": 162, "column": 17}}, "162": {"start": {"line": 163, "column": 0}, "end": {"line": 163, "column": 24}}, "163": {"start": {"line": 164, "column": 0}, "end": {"line": 164, "column": 28}}, "164": {"start": {"line": 165, "column": 0}, "end": {"line": 165, "column": 30}}, "165": {"start": {"line": 166, "column": 0}, "end": {"line": 166, "column": 30}}, "166": {"start": {"line": 167, "column": 0}, "end": {"line": 167, "column": 32}}, "167": {"start": {"line": 168, "column": 0}, "end": {"line": 168, "column": 12}}, "168": {"start": {"line": 169, "column": 0}, "end": {"line": 169, "column": 24}}, "169": {"start": {"line": 170, "column": 0}, "end": {"line": 170, "column": 31}}, "170": {"start": {"line": 171, "column": 0}, "end": {"line": 171, "column": 37}}, "171": {"start": {"line": 172, "column": 0}, "end": {"line": 172, "column": 35}}, "172": {"start": {"line": 173, "column": 0}, "end": {"line": 173, "column": 12}}, "173": {"start": {"line": 174, "column": 0}, "end": {"line": 174, "column": 41}}, "174": {"start": {"line": 175, "column": 0}, "end": {"line": 175, "column": 9}}, "175": {"start": {"line": 176, "column": 0}, "end": {"line": 176, "column": 23}}, "176": {"start": {"line": 177, "column": 0}, "end": {"line": 177, "column": 41}}, "177": {"start": {"line": 178, "column": 0}, "end": {"line": 178, "column": 21}}, "178": {"start": {"line": 179, "column": 0}, "end": {"line": 179, "column": 9}}, "180": {"start": {"line": 181, "column": 0}, "end": {"line": 181, "column": 29}}, "181": {"start": {"line": 182, "column": 0}, "end": {"line": 182, "column": 40}}, "182": {"start": {"line": 183, "column": 0}, "end": {"line": 183, "column": 81}}, "183": {"start": {"line": 184, "column": 0}, "end": {"line": 184, "column": 10}}, "184": {"start": {"line": 185, "column": 0}, "end": {"line": 185, "column": 7}}, "185": {"start": {"line": 186, "column": 0}, "end": {"line": 186, "column": 7}}, "188": {"start": {"line": 189, "column": 0}, "end": {"line": 189, "column": 38}}, "189": {"start": {"line": 190, "column": 0}, "end": {"line": 190, "column": 21}}, "190": {"start": {"line": 191, "column": 0}, "end": {"line": 191, "column": 60}}, "191": {"start": {"line": 192, "column": 0}, "end": {"line": 192, "column": 7}}, "192": {"start": {"line": 193, "column": 0}, "end": {"line": 193, "column": 41}}, "193": {"start": {"line": 194, "column": 0}, "end": {"line": 194, "column": 11}}, "194": {"start": {"line": 195, "column": 0}, "end": {"line": 195, "column": 84}}, "195": {"start": {"line": 196, "column": 0}, "end": {"line": 196, "column": 28}}, "196": {"start": {"line": 197, "column": 0}, "end": {"line": 197, "column": 9}}, "198": {"start": {"line": 199, "column": 0}, "end": {"line": 199, "column": 23}}, "199": {"start": {"line": 200, "column": 0}, "end": {"line": 200, "column": 31}}, "200": {"start": {"line": 201, "column": 0}, "end": {"line": 201, "column": 33}}, "201": {"start": {"line": 202, "column": 0}, "end": {"line": 202, "column": 44}}, "202": {"start": {"line": 203, "column": 0}, "end": {"line": 203, "column": 12}}, "203": {"start": {"line": 204, "column": 0}, "end": {"line": 204, "column": 9}}, "205": {"start": {"line": 206, "column": 0}, "end": {"line": 206, "column": 16}}, "206": {"start": {"line": 207, "column": 0}, "end": {"line": 207, "column": 40}}, "207": {"start": {"line": 208, "column": 0}, "end": {"line": 208, "column": 33}}, "208": {"start": {"line": 209, "column": 0}, "end": {"line": 209, "column": 9}}, "209": {"start": {"line": 210, "column": 0}, "end": {"line": 210, "column": 23}}, "210": {"start": {"line": 211, "column": 0}, "end": {"line": 211, "column": 41}}, "211": {"start": {"line": 212, "column": 0}, "end": {"line": 212, "column": 21}}, "212": {"start": {"line": 213, "column": 0}, "end": {"line": 213, "column": 9}}, "214": {"start": {"line": 215, "column": 0}, "end": {"line": 215, "column": 29}}, "215": {"start": {"line": 216, "column": 0}, "end": {"line": 216, "column": 40}}, "216": {"start": {"line": 217, "column": 0}, "end": {"line": 217, "column": 81}}, "217": {"start": {"line": 218, "column": 0}, "end": {"line": 218, "column": 10}}, "218": {"start": {"line": 219, "column": 0}, "end": {"line": 219, "column": 7}}, "219": {"start": {"line": 220, "column": 0}, "end": {"line": 220, "column": 7}}, "222": {"start": {"line": 223, "column": 0}, "end": {"line": 223, "column": 37}}, "223": {"start": {"line": 224, "column": 0}, "end": {"line": 224, "column": 21}}, "224": {"start": {"line": 225, "column": 0}, "end": {"line": 225, "column": 60}}, "225": {"start": {"line": 226, "column": 0}, "end": {"line": 226, "column": 7}}, "226": {"start": {"line": 227, "column": 0}, "end": {"line": 227, "column": 41}}, "227": {"start": {"line": 228, "column": 0}, "end": {"line": 228, "column": 11}}, "228": {"start": {"line": 229, "column": 0}, "end": {"line": 229, "column": 83}}, "229": {"start": {"line": 230, "column": 0}, "end": {"line": 230, "column": 28}}, "230": {"start": {"line": 231, "column": 0}, "end": {"line": 231, "column": 9}}, "232": {"start": {"line": 233, "column": 0}, "end": {"line": 233, "column": 16}}, "233": {"start": {"line": 234, "column": 0}, "end": {"line": 234, "column": 72}}, "234": {"start": {"line": 235, "column": 0}, "end": {"line": 235, "column": 27}}, "235": {"start": {"line": 236, "column": 0}, "end": {"line": 236, "column": 9}}, "236": {"start": {"line": 237, "column": 0}, "end": {"line": 237, "column": 23}}, "237": {"start": {"line": 238, "column": 0}, "end": {"line": 238, "column": 29}}, "238": {"start": {"line": 239, "column": 0}, "end": {"line": 239, "column": 40}}, "239": {"start": {"line": 240, "column": 0}, "end": {"line": 240, "column": 81}}, "240": {"start": {"line": 241, "column": 0}, "end": {"line": 241, "column": 10}}, "241": {"start": {"line": 242, "column": 0}, "end": {"line": 242, "column": 7}}, "242": {"start": {"line": 243, "column": 0}, "end": {"line": 243, "column": 7}}, "243": {"start": {"line": 244, "column": 0}, "end": {"line": 244, "column": 2}}}, "s": {"0": 0, "5": 0, "6": 0, "7": 0, "8": 0, "11": 0, "12": 0, "13": 0, "14": 0, "16": 0, "18": 0, "19": 0, "20": 0, "21": 0, "23": 0, "24": 0, "25": 0, "26": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "73": 0, "74": 0, "75": 0, "76": 0, "78": 0, "79": 0, "80": 0, "81": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "91": 0, "92": 0, "93": 0, "94": 0, "95": 0, "96": 0, "99": 0, "100": 0, "101": 0, "102": 0, "104": 0, "105": 0, "106": 0, "107": 0, "108": 0, "109": 0, "110": 0, "111": 0, "112": 0, "114": 0, "115": 0, "116": 0, "117": 0, "118": 0, "119": 0, "122": 0, "123": 0, "124": 0, "125": 0, "126": 0, "127": 0, "128": 0, "129": 0, "130": 0, "132": 0, "133": 0, "134": 0, "135": 0, "136": 0, "137": 0, "140": 0, "141": 0, "142": 0, "143": 0, "144": 0, "145": 0, "146": 0, "149": 0, "150": 0, "151": 0, "152": 0, "153": 0, "154": 0, "155": 0, "156": 0, "157": 0, "159": 0, "160": 0, "161": 0, "162": 0, "163": 0, "164": 0, "165": 0, "166": 0, "167": 0, "168": 0, "169": 0, "170": 0, "171": 0, "172": 0, "173": 0, "174": 0, "175": 0, "176": 0, "177": 0, "178": 0, "180": 0, "181": 0, "182": 0, "183": 0, "184": 0, "185": 0, "188": 0, "189": 0, "190": 0, "191": 0, "192": 0, "193": 0, "194": 0, "195": 0, "196": 0, "198": 0, "199": 0, "200": 0, "201": 0, "202": 0, "203": 0, "205": 0, "206": 0, "207": 0, "208": 0, "209": 0, "210": 0, "211": 0, "212": 0, "214": 0, "215": 0, "216": 0, "217": 0, "218": 0, "219": 0, "222": 0, "223": 0, "224": 0, "225": 0, "226": 0, "227": 0, "228": 0, "229": 0, "230": 0, "232": 0, "233": 0, "234": 0, "235": 0, "236": 0, "237": 0, "238": 0, "239": 0, "240": 0, "241": 0, "242": 0, "243": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 244, "column": -317}}, "locations": [{"start": {"line": 1, "column": 0}, "end": {"line": 244, "column": -317}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 0}, "end": {"line": 244, "column": -317}}, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 244, "column": -317}}, "line": 1}}, "f": {"0": 0}}, "/Users/<USER>/code/open-trpc/packages/server-core/src/api/routers/order.router.ts": {"path": "/Users/<USER>/code/open-trpc/packages/server-core/src/api/routers/order.router.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 23}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 36}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 47}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 41}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 53}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 58}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 2}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 34}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 47}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 61}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 39}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 32}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 30}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 35}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 33}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 32}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 2}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 32}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 41}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 2}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 32}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 40}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 2}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 35}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 28}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 29}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 41}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 22}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 29}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 31}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 27}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 10}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 7}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 11}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 106}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 27}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 31}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 30}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 29}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 12}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 9}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 49}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 31}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 30}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 31}}, "57": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 12}}, "58": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 9}}, "60": {"start": {"line": 61, "column": 0}, "end": {"line": 61, "column": 78}}, "61": {"start": {"line": 62, "column": 0}, "end": {"line": 62, "column": 30}}, "62": {"start": {"line": 63, "column": 0}, "end": {"line": 63, "column": 45}}, "63": {"start": {"line": 64, "column": 0}, "end": {"line": 64, "column": 31}}, "64": {"start": {"line": 65, "column": 0}, "end": {"line": 65, "column": 27}}, "65": {"start": {"line": 66, "column": 0}, "end": {"line": 66, "column": 33}}, "66": {"start": {"line": 67, "column": 0}, "end": {"line": 67, "column": 10}}, "68": {"start": {"line": 69, "column": 0}, "end": {"line": 69, "column": 16}}, "69": {"start": {"line": 70, "column": 0}, "end": {"line": 70, "column": 28}}, "70": {"start": {"line": 71, "column": 0}, "end": {"line": 71, "column": 18}}, "71": {"start": {"line": 72, "column": 0}, "end": {"line": 72, "column": 9}}, "72": {"start": {"line": 73, "column": 0}, "end": {"line": 73, "column": 23}}, "73": {"start": {"line": 74, "column": 0}, "end": {"line": 74, "column": 41}}, "74": {"start": {"line": 75, "column": 0}, "end": {"line": 75, "column": 21}}, "75": {"start": {"line": 76, "column": 0}, "end": {"line": 76, "column": 9}}, "77": {"start": {"line": 78, "column": 0}, "end": {"line": 78, "column": 29}}, "78": {"start": {"line": 79, "column": 0}, "end": {"line": 79, "column": 40}}, "79": {"start": {"line": 80, "column": 0}, "end": {"line": 80, "column": 69}}, "80": {"start": {"line": 81, "column": 0}, "end": {"line": 81, "column": 10}}, "81": {"start": {"line": 82, "column": 0}, "end": {"line": 82, "column": 7}}, "82": {"start": {"line": 83, "column": 0}, "end": {"line": 83, "column": 7}}, "85": {"start": {"line": 86, "column": 0}, "end": {"line": 86, "column": 26}}, "86": {"start": {"line": 87, "column": 0}, "end": {"line": 87, "column": 27}}, "87": {"start": {"line": 88, "column": 0}, "end": {"line": 88, "column": 38}}, "88": {"start": {"line": 89, "column": 0}, "end": {"line": 89, "column": 22}}, "89": {"start": {"line": 90, "column": 0}, "end": {"line": 90, "column": 29}}, "90": {"start": {"line": 91, "column": 0}, "end": {"line": 91, "column": 31}}, "91": {"start": {"line": 92, "column": 0}, "end": {"line": 92, "column": 27}}, "92": {"start": {"line": 93, "column": 0}, "end": {"line": 93, "column": 10}}, "93": {"start": {"line": 94, "column": 0}, "end": {"line": 94, "column": 7}}, "95": {"start": {"line": 96, "column": 0}, "end": {"line": 96, "column": 11}}, "96": {"start": {"line": 97, "column": 0}, "end": {"line": 97, "column": 70}}, "97": {"start": {"line": 98, "column": 0}, "end": {"line": 98, "column": 19}}, "98": {"start": {"line": 99, "column": 0}, "end": {"line": 99, "column": 43}}, "99": {"start": {"line": 100, "column": 0}, "end": {"line": 100, "column": 10}}, "101": {"start": {"line": 102, "column": 0}, "end": {"line": 102, "column": 21}}, "102": {"start": {"line": 103, "column": 0}, "end": {"line": 103, "column": 23}}, "103": {"start": {"line": 104, "column": 0}, "end": {"line": 104, "column": 29}}, "104": {"start": {"line": 105, "column": 0}, "end": {"line": 105, "column": 40}}, "105": {"start": {"line": 106, "column": 0}, "end": {"line": 106, "column": 71}}, "106": {"start": {"line": 107, "column": 0}, "end": {"line": 107, "column": 10}}, "107": {"start": {"line": 108, "column": 0}, "end": {"line": 108, "column": 7}}, "108": {"start": {"line": 109, "column": 0}, "end": {"line": 109, "column": 7}}, "111": {"start": {"line": 112, "column": 0}, "end": {"line": 112, "column": 28}}, "112": {"start": {"line": 113, "column": 0}, "end": {"line": 113, "column": 25}}, "113": {"start": {"line": 114, "column": 0}, "end": {"line": 114, "column": 38}}, "114": {"start": {"line": 115, "column": 0}, "end": {"line": 115, "column": 22}}, "115": {"start": {"line": 116, "column": 0}, "end": {"line": 116, "column": 29}}, "116": {"start": {"line": 117, "column": 0}, "end": {"line": 117, "column": 31}}, "117": {"start": {"line": 118, "column": 0}, "end": {"line": 118, "column": 27}}, "118": {"start": {"line": 119, "column": 0}, "end": {"line": 119, "column": 10}}, "119": {"start": {"line": 120, "column": 0}, "end": {"line": 120, "column": 7}}, "121": {"start": {"line": 122, "column": 0}, "end": {"line": 122, "column": 11}}, "122": {"start": {"line": 123, "column": 0}, "end": {"line": 123, "column": 82}}, "124": {"start": {"line": 125, "column": 0}, "end": {"line": 125, "column": 21}}, "125": {"start": {"line": 126, "column": 0}, "end": {"line": 126, "column": 31}}, "126": {"start": {"line": 127, "column": 0}, "end": {"line": 127, "column": 30}}, "127": {"start": {"line": 128, "column": 0}, "end": {"line": 128, "column": 29}}, "128": {"start": {"line": 129, "column": 0}, "end": {"line": 129, "column": 12}}, "129": {"start": {"line": 130, "column": 0}, "end": {"line": 130, "column": 9}}, "132": {"start": {"line": 133, "column": 0}, "end": {"line": 133, "column": 43}}, "133": {"start": {"line": 134, "column": 0}, "end": {"line": 134, "column": 31}}, "134": {"start": {"line": 135, "column": 0}, "end": {"line": 135, "column": 30}}, "135": {"start": {"line": 136, "column": 0}, "end": {"line": 136, "column": 31}}, "136": {"start": {"line": 137, "column": 0}, "end": {"line": 137, "column": 12}}, "137": {"start": {"line": 138, "column": 0}, "end": {"line": 138, "column": 9}}, "139": {"start": {"line": 140, "column": 0}, "end": {"line": 140, "column": 20}}, "140": {"start": {"line": 141, "column": 0}, "end": {"line": 141, "column": 23}}, "141": {"start": {"line": 142, "column": 0}, "end": {"line": 142, "column": 41}}, "142": {"start": {"line": 143, "column": 0}, "end": {"line": 143, "column": 21}}, "143": {"start": {"line": 144, "column": 0}, "end": {"line": 144, "column": 9}}, "145": {"start": {"line": 146, "column": 0}, "end": {"line": 146, "column": 29}}, "146": {"start": {"line": 147, "column": 0}, "end": {"line": 147, "column": 40}}, "147": {"start": {"line": 148, "column": 0}, "end": {"line": 148, "column": 71}}, "148": {"start": {"line": 149, "column": 0}, "end": {"line": 149, "column": 10}}, "149": {"start": {"line": 150, "column": 0}, "end": {"line": 150, "column": 7}}, "150": {"start": {"line": 151, "column": 0}, "end": {"line": 151, "column": 7}}, "153": {"start": {"line": 154, "column": 0}, "end": {"line": 154, "column": 34}}, "154": {"start": {"line": 155, "column": 0}, "end": {"line": 155, "column": 25}}, "155": {"start": {"line": 156, "column": 0}, "end": {"line": 156, "column": 38}}, "156": {"start": {"line": 157, "column": 0}, "end": {"line": 157, "column": 22}}, "157": {"start": {"line": 158, "column": 0}, "end": {"line": 158, "column": 29}}, "158": {"start": {"line": 159, "column": 0}, "end": {"line": 159, "column": 31}}, "159": {"start": {"line": 160, "column": 0}, "end": {"line": 160, "column": 27}}, "160": {"start": {"line": 161, "column": 0}, "end": {"line": 161, "column": 10}}, "161": {"start": {"line": 162, "column": 0}, "end": {"line": 162, "column": 7}}, "163": {"start": {"line": 164, "column": 0}, "end": {"line": 164, "column": 11}}, "164": {"start": {"line": 165, "column": 0}, "end": {"line": 165, "column": 87}}, "166": {"start": {"line": 167, "column": 0}, "end": {"line": 167, "column": 21}}, "167": {"start": {"line": 168, "column": 0}, "end": {"line": 168, "column": 31}}, "168": {"start": {"line": 169, "column": 0}, "end": {"line": 169, "column": 30}}, "169": {"start": {"line": 170, "column": 0}, "end": {"line": 170, "column": 29}}, "170": {"start": {"line": 171, "column": 0}, "end": {"line": 171, "column": 12}}, "171": {"start": {"line": 172, "column": 0}, "end": {"line": 172, "column": 9}}, "174": {"start": {"line": 175, "column": 0}, "end": {"line": 175, "column": 43}}, "175": {"start": {"line": 176, "column": 0}, "end": {"line": 176, "column": 31}}, "176": {"start": {"line": 177, "column": 0}, "end": {"line": 177, "column": 30}}, "177": {"start": {"line": 178, "column": 0}, "end": {"line": 178, "column": 31}}, "178": {"start": {"line": 179, "column": 0}, "end": {"line": 179, "column": 12}}, "179": {"start": {"line": 180, "column": 0}, "end": {"line": 180, "column": 9}}, "181": {"start": {"line": 182, "column": 0}, "end": {"line": 182, "column": 20}}, "182": {"start": {"line": 183, "column": 0}, "end": {"line": 183, "column": 23}}, "183": {"start": {"line": 184, "column": 0}, "end": {"line": 184, "column": 41}}, "184": {"start": {"line": 185, "column": 0}, "end": {"line": 185, "column": 21}}, "185": {"start": {"line": 186, "column": 0}, "end": {"line": 186, "column": 9}}, "187": {"start": {"line": 188, "column": 0}, "end": {"line": 188, "column": 29}}, "188": {"start": {"line": 189, "column": 0}, "end": {"line": 189, "column": 40}}, "189": {"start": {"line": 190, "column": 0}, "end": {"line": 190, "column": 69}}, "190": {"start": {"line": 191, "column": 0}, "end": {"line": 191, "column": 10}}, "191": {"start": {"line": 192, "column": 0}, "end": {"line": 192, "column": 7}}, "192": {"start": {"line": 193, "column": 0}, "end": {"line": 193, "column": 7}}, "195": {"start": {"line": 196, "column": 0}, "end": {"line": 196, "column": 28}}, "196": {"start": {"line": 197, "column": 0}, "end": {"line": 197, "column": 25}}, "197": {"start": {"line": 198, "column": 0}, "end": {"line": 198, "column": 41}}, "198": {"start": {"line": 199, "column": 0}, "end": {"line": 199, "column": 22}}, "199": {"start": {"line": 200, "column": 0}, "end": {"line": 200, "column": 29}}, "200": {"start": {"line": 201, "column": 0}, "end": {"line": 201, "column": 31}}, "201": {"start": {"line": 202, "column": 0}, "end": {"line": 202, "column": 27}}, "202": {"start": {"line": 203, "column": 0}, "end": {"line": 203, "column": 10}}, "203": {"start": {"line": 204, "column": 0}, "end": {"line": 204, "column": 7}}, "205": {"start": {"line": 206, "column": 0}, "end": {"line": 206, "column": 11}}, "206": {"start": {"line": 207, "column": 0}, "end": {"line": 207, "column": 82}}, "208": {"start": {"line": 209, "column": 0}, "end": {"line": 209, "column": 21}}, "209": {"start": {"line": 210, "column": 0}, "end": {"line": 210, "column": 31}}, "210": {"start": {"line": 211, "column": 0}, "end": {"line": 211, "column": 30}}, "211": {"start": {"line": 212, "column": 0}, "end": {"line": 212, "column": 29}}, "212": {"start": {"line": 213, "column": 0}, "end": {"line": 213, "column": 12}}, "213": {"start": {"line": 214, "column": 0}, "end": {"line": 214, "column": 9}}, "216": {"start": {"line": 217, "column": 0}, "end": {"line": 217, "column": 43}}, "217": {"start": {"line": 218, "column": 0}, "end": {"line": 218, "column": 31}}, "218": {"start": {"line": 219, "column": 0}, "end": {"line": 219, "column": 30}}, "219": {"start": {"line": 220, "column": 0}, "end": {"line": 220, "column": 31}}, "220": {"start": {"line": 221, "column": 0}, "end": {"line": 221, "column": 12}}, "221": {"start": {"line": 222, "column": 0}, "end": {"line": 222, "column": 9}}, "223": {"start": {"line": 224, "column": 0}, "end": {"line": 224, "column": 90}}, "225": {"start": {"line": 226, "column": 0}, "end": {"line": 226, "column": 30}}, "226": {"start": {"line": 227, "column": 0}, "end": {"line": 227, "column": 31}}, "227": {"start": {"line": 228, "column": 0}, "end": {"line": 228, "column": 42}}, "228": {"start": {"line": 229, "column": 0}, "end": {"line": 229, "column": 30}}, "229": {"start": {"line": 230, "column": 0}, "end": {"line": 230, "column": 12}}, "230": {"start": {"line": 231, "column": 0}, "end": {"line": 231, "column": 9}}, "232": {"start": {"line": 233, "column": 0}, "end": {"line": 233, "column": 16}}, "233": {"start": {"line": 234, "column": 0}, "end": {"line": 234, "column": 28}}, "234": {"start": {"line": 235, "column": 0}, "end": {"line": 235, "column": 32}}, "235": {"start": {"line": 236, "column": 0}, "end": {"line": 236, "column": 9}}, "236": {"start": {"line": 237, "column": 0}, "end": {"line": 237, "column": 23}}, "237": {"start": {"line": 238, "column": 0}, "end": {"line": 238, "column": 41}}, "238": {"start": {"line": 239, "column": 0}, "end": {"line": 239, "column": 21}}, "239": {"start": {"line": 240, "column": 0}, "end": {"line": 240, "column": 9}}, "241": {"start": {"line": 242, "column": 0}, "end": {"line": 242, "column": 37}}, "242": {"start": {"line": 243, "column": 0}, "end": {"line": 243, "column": 52}}, "243": {"start": {"line": 244, "column": 0}, "end": {"line": 244, "column": 33}}, "244": {"start": {"line": 245, "column": 0}, "end": {"line": 245, "column": 32}}, "245": {"start": {"line": 246, "column": 0}, "end": {"line": 246, "column": 31}}, "246": {"start": {"line": 247, "column": 0}, "end": {"line": 247, "column": 14}}, "247": {"start": {"line": 248, "column": 0}, "end": {"line": 248, "column": 11}}, "248": {"start": {"line": 249, "column": 0}, "end": {"line": 249, "column": 66}}, "249": {"start": {"line": 250, "column": 0}, "end": {"line": 250, "column": 33}}, "250": {"start": {"line": 251, "column": 0}, "end": {"line": 251, "column": 34}}, "251": {"start": {"line": 252, "column": 0}, "end": {"line": 252, "column": 36}}, "252": {"start": {"line": 253, "column": 0}, "end": {"line": 253, "column": 14}}, "253": {"start": {"line": 254, "column": 0}, "end": {"line": 254, "column": 11}}, "254": {"start": {"line": 255, "column": 0}, "end": {"line": 255, "column": 9}}, "256": {"start": {"line": 257, "column": 0}, "end": {"line": 257, "column": 29}}, "257": {"start": {"line": 258, "column": 0}, "end": {"line": 258, "column": 40}}, "258": {"start": {"line": 259, "column": 0}, "end": {"line": 259, "column": 69}}, "259": {"start": {"line": 260, "column": 0}, "end": {"line": 260, "column": 10}}, "260": {"start": {"line": 261, "column": 0}, "end": {"line": 261, "column": 7}}, "261": {"start": {"line": 262, "column": 0}, "end": {"line": 262, "column": 7}}, "264": {"start": {"line": 265, "column": 0}, "end": {"line": 265, "column": 36}}, "265": {"start": {"line": 266, "column": 0}, "end": {"line": 266, "column": 25}}, "266": {"start": {"line": 267, "column": 0}, "end": {"line": 267, "column": 41}}, "267": {"start": {"line": 268, "column": 0}, "end": {"line": 268, "column": 22}}, "268": {"start": {"line": 269, "column": 0}, "end": {"line": 269, "column": 29}}, "269": {"start": {"line": 270, "column": 0}, "end": {"line": 270, "column": 31}}, "270": {"start": {"line": 271, "column": 0}, "end": {"line": 271, "column": 27}}, "271": {"start": {"line": 272, "column": 0}, "end": {"line": 272, "column": 10}}, "272": {"start": {"line": 273, "column": 0}, "end": {"line": 273, "column": 7}}, "274": {"start": {"line": 275, "column": 0}, "end": {"line": 275, "column": 11}}, "275": {"start": {"line": 276, "column": 0}, "end": {"line": 276, "column": 82}}, "277": {"start": {"line": 278, "column": 0}, "end": {"line": 278, "column": 21}}, "278": {"start": {"line": 279, "column": 0}, "end": {"line": 279, "column": 31}}, "279": {"start": {"line": 280, "column": 0}, "end": {"line": 280, "column": 30}}, "280": {"start": {"line": 281, "column": 0}, "end": {"line": 281, "column": 29}}, "281": {"start": {"line": 282, "column": 0}, "end": {"line": 282, "column": 12}}, "282": {"start": {"line": 283, "column": 0}, "end": {"line": 283, "column": 9}}, "285": {"start": {"line": 286, "column": 0}, "end": {"line": 286, "column": 43}}, "286": {"start": {"line": 287, "column": 0}, "end": {"line": 287, "column": 31}}, "287": {"start": {"line": 288, "column": 0}, "end": {"line": 288, "column": 30}}, "288": {"start": {"line": 289, "column": 0}, "end": {"line": 289, "column": 31}}, "289": {"start": {"line": 290, "column": 0}, "end": {"line": 290, "column": 12}}, "290": {"start": {"line": 291, "column": 0}, "end": {"line": 291, "column": 9}}, "292": {"start": {"line": 293, "column": 0}, "end": {"line": 293, "column": 91}}, "294": {"start": {"line": 295, "column": 0}, "end": {"line": 295, "column": 28}}, "295": {"start": {"line": 296, "column": 0}, "end": {"line": 296, "column": 31}}, "296": {"start": {"line": 297, "column": 0}, "end": {"line": 297, "column": 42}}, "297": {"start": {"line": 298, "column": 0}, "end": {"line": 298, "column": 30}}, "298": {"start": {"line": 299, "column": 0}, "end": {"line": 299, "column": 12}}, "299": {"start": {"line": 300, "column": 0}, "end": {"line": 300, "column": 9}}, "301": {"start": {"line": 302, "column": 0}, "end": {"line": 302, "column": 16}}, "302": {"start": {"line": 303, "column": 0}, "end": {"line": 303, "column": 28}}, "303": {"start": {"line": 304, "column": 0}, "end": {"line": 304, "column": 30}}, "304": {"start": {"line": 305, "column": 0}, "end": {"line": 305, "column": 9}}, "305": {"start": {"line": 306, "column": 0}, "end": {"line": 306, "column": 23}}, "306": {"start": {"line": 307, "column": 0}, "end": {"line": 307, "column": 41}}, "307": {"start": {"line": 308, "column": 0}, "end": {"line": 308, "column": 21}}, "308": {"start": {"line": 309, "column": 0}, "end": {"line": 309, "column": 9}}, "310": {"start": {"line": 311, "column": 0}, "end": {"line": 311, "column": 29}}, "311": {"start": {"line": 312, "column": 0}, "end": {"line": 312, "column": 40}}, "312": {"start": {"line": 313, "column": 0}, "end": {"line": 313, "column": 69}}, "313": {"start": {"line": 314, "column": 0}, "end": {"line": 314, "column": 10}}, "314": {"start": {"line": 315, "column": 0}, "end": {"line": 315, "column": 7}}, "315": {"start": {"line": 316, "column": 0}, "end": {"line": 316, "column": 7}}, "318": {"start": {"line": 319, "column": 0}, "end": {"line": 319, "column": 27}}, "319": {"start": {"line": 320, "column": 0}, "end": {"line": 320, "column": 31}}, "320": {"start": {"line": 321, "column": 0}, "end": {"line": 321, "column": 22}}, "321": {"start": {"line": 322, "column": 0}, "end": {"line": 322, "column": 29}}, "322": {"start": {"line": 323, "column": 0}, "end": {"line": 323, "column": 31}}, "323": {"start": {"line": 324, "column": 0}, "end": {"line": 324, "column": 27}}, "324": {"start": {"line": 325, "column": 0}, "end": {"line": 325, "column": 10}}, "325": {"start": {"line": 326, "column": 0}, "end": {"line": 326, "column": 7}}, "327": {"start": {"line": 328, "column": 0}, "end": {"line": 328, "column": 11}}, "328": {"start": {"line": 329, "column": 0}, "end": {"line": 329, "column": 85}}, "329": {"start": {"line": 330, "column": 0}, "end": {"line": 330, "column": 20}}, "330": {"start": {"line": 331, "column": 0}, "end": {"line": 331, "column": 23}}, "331": {"start": {"line": 332, "column": 0}, "end": {"line": 332, "column": 29}}, "332": {"start": {"line": 333, "column": 0}, "end": {"line": 333, "column": 40}}, "333": {"start": {"line": 334, "column": 0}, "end": {"line": 334, "column": 71}}, "334": {"start": {"line": 335, "column": 0}, "end": {"line": 335, "column": 10}}, "335": {"start": {"line": 336, "column": 0}, "end": {"line": 336, "column": 7}}, "336": {"start": {"line": 337, "column": 0}, "end": {"line": 337, "column": 7}}, "337": {"start": {"line": 338, "column": 0}, "end": {"line": 338, "column": 2}}}, "s": {"0": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "23": 0, "24": 0, "25": 0, "27": 0, "28": 0, "29": 0, "31": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "43": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "90": 0, "91": 0, "92": 0, "93": 0, "95": 0, "96": 0, "97": 0, "98": 0, "99": 0, "101": 0, "102": 0, "103": 0, "104": 0, "105": 0, "106": 0, "107": 0, "108": 0, "111": 0, "112": 0, "113": 0, "114": 0, "115": 0, "116": 0, "117": 0, "118": 0, "119": 0, "121": 0, "122": 0, "124": 0, "125": 0, "126": 0, "127": 0, "128": 0, "129": 0, "132": 0, "133": 0, "134": 0, "135": 0, "136": 0, "137": 0, "139": 0, "140": 0, "141": 0, "142": 0, "143": 0, "145": 0, "146": 0, "147": 0, "148": 0, "149": 0, "150": 0, "153": 0, "154": 0, "155": 0, "156": 0, "157": 0, "158": 0, "159": 0, "160": 0, "161": 0, "163": 0, "164": 0, "166": 0, "167": 0, "168": 0, "169": 0, "170": 0, "171": 0, "174": 0, "175": 0, "176": 0, "177": 0, "178": 0, "179": 0, "181": 0, "182": 0, "183": 0, "184": 0, "185": 0, "187": 0, "188": 0, "189": 0, "190": 0, "191": 0, "192": 0, "195": 0, "196": 0, "197": 0, "198": 0, "199": 0, "200": 0, "201": 0, "202": 0, "203": 0, "205": 0, "206": 0, "208": 0, "209": 0, "210": 0, "211": 0, "212": 0, "213": 0, "216": 0, "217": 0, "218": 0, "219": 0, "220": 0, "221": 0, "223": 0, "225": 0, "226": 0, "227": 0, "228": 0, "229": 0, "230": 0, "232": 0, "233": 0, "234": 0, "235": 0, "236": 0, "237": 0, "238": 0, "239": 0, "241": 0, "242": 0, "243": 0, "244": 0, "245": 0, "246": 0, "247": 0, "248": 0, "249": 0, "250": 0, "251": 0, "252": 0, "253": 0, "254": 0, "256": 0, "257": 0, "258": 0, "259": 0, "260": 0, "261": 0, "264": 0, "265": 0, "266": 0, "267": 0, "268": 0, "269": 0, "270": 0, "271": 0, "272": 0, "274": 0, "275": 0, "277": 0, "278": 0, "279": 0, "280": 0, "281": 0, "282": 0, "285": 0, "286": 0, "287": 0, "288": 0, "289": 0, "290": 0, "292": 0, "294": 0, "295": 0, "296": 0, "297": 0, "298": 0, "299": 0, "301": 0, "302": 0, "303": 0, "304": 0, "305": 0, "306": 0, "307": 0, "308": 0, "310": 0, "311": 0, "312": 0, "313": 0, "314": 0, "315": 0, "318": 0, "319": 0, "320": 0, "321": 0, "322": 0, "323": 0, "324": 0, "325": 0, "327": 0, "328": 0, "329": 0, "330": 0, "331": 0, "332": 0, "333": 0, "334": 0, "335": 0, "336": 0, "337": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 338, "column": -760}}, "locations": [{"start": {"line": 1, "column": 0}, "end": {"line": 338, "column": -760}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 0}, "end": {"line": 338, "column": -760}}, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 338, "column": -760}}, "line": 1}}, "f": {"0": 0}}, "/Users/<USER>/code/open-trpc/packages/server-core/src/api/routers/user.router.ts": {"path": "/Users/<USER>/code/open-trpc/packages/server-core/src/api/routers/user.router.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 23}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 38}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 47}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 51}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 67}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 2}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 39}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 44}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 44}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 2}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 36}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 40}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 2}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 37}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 43}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 2}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 34}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 24}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 31}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 22}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 29}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 31}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 27}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 10}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 7}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 51}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 21}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 7}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 35}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 31}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 41}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 22}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 29}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 31}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 27}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 10}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 7}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 11}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 74}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 22}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 15}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 9}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 27}}, "57": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 31}}, "58": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 42}}, "59": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 28}}, "60": {"start": {"line": 61, "column": 0}, "end": {"line": 61, "column": 12}}, "61": {"start": {"line": 62, "column": 0}, "end": {"line": 62, "column": 9}}, "63": {"start": {"line": 64, "column": 0}, "end": {"line": 64, "column": 16}}, "64": {"start": {"line": 65, "column": 0}, "end": {"line": 65, "column": 26}}, "65": {"start": {"line": 66, "column": 0}, "end": {"line": 66, "column": 28}}, "66": {"start": {"line": 67, "column": 0}, "end": {"line": 67, "column": 9}}, "67": {"start": {"line": 68, "column": 0}, "end": {"line": 68, "column": 23}}, "68": {"start": {"line": 69, "column": 0}, "end": {"line": 69, "column": 37}}, "69": {"start": {"line": 70, "column": 0}, "end": {"line": 70, "column": 57}}, "70": {"start": {"line": 71, "column": 0}, "end": {"line": 71, "column": 33}}, "71": {"start": {"line": 72, "column": 0}, "end": {"line": 72, "column": 34}}, "72": {"start": {"line": 73, "column": 0}, "end": {"line": 73, "column": 36}}, "73": {"start": {"line": 74, "column": 0}, "end": {"line": 74, "column": 14}}, "74": {"start": {"line": 75, "column": 0}, "end": {"line": 75, "column": 11}}, "75": {"start": {"line": 76, "column": 0}, "end": {"line": 76, "column": 57}}, "76": {"start": {"line": 77, "column": 0}, "end": {"line": 77, "column": 33}}, "77": {"start": {"line": 78, "column": 0}, "end": {"line": 78, "column": 34}}, "78": {"start": {"line": 79, "column": 0}, "end": {"line": 79, "column": 37}}, "79": {"start": {"line": 80, "column": 0}, "end": {"line": 80, "column": 14}}, "80": {"start": {"line": 81, "column": 0}, "end": {"line": 81, "column": 11}}, "81": {"start": {"line": 82, "column": 0}, "end": {"line": 82, "column": 9}}, "83": {"start": {"line": 84, "column": 0}, "end": {"line": 84, "column": 29}}, "84": {"start": {"line": 85, "column": 0}, "end": {"line": 85, "column": 40}}, "85": {"start": {"line": 86, "column": 0}, "end": {"line": 86, "column": 67}}, "86": {"start": {"line": 87, "column": 0}, "end": {"line": 87, "column": 10}}, "87": {"start": {"line": 88, "column": 0}, "end": {"line": 88, "column": 7}}, "88": {"start": {"line": 89, "column": 0}, "end": {"line": 89, "column": 7}}, "91": {"start": {"line": 92, "column": 0}, "end": {"line": 92, "column": 36}}, "92": {"start": {"line": 93, "column": 0}, "end": {"line": 93, "column": 32}}, "93": {"start": {"line": 94, "column": 0}, "end": {"line": 94, "column": 41}}, "94": {"start": {"line": 95, "column": 0}, "end": {"line": 95, "column": 22}}, "95": {"start": {"line": 96, "column": 0}, "end": {"line": 96, "column": 29}}, "96": {"start": {"line": 97, "column": 0}, "end": {"line": 97, "column": 31}}, "97": {"start": {"line": 98, "column": 0}, "end": {"line": 98, "column": 27}}, "98": {"start": {"line": 99, "column": 0}, "end": {"line": 99, "column": 10}}, "99": {"start": {"line": 100, "column": 0}, "end": {"line": 100, "column": 7}}, "101": {"start": {"line": 102, "column": 0}, "end": {"line": 102, "column": 11}}, "102": {"start": {"line": 103, "column": 0}, "end": {"line": 103, "column": 71}}, "103": {"start": {"line": 104, "column": 0}, "end": {"line": 104, "column": 22}}, "104": {"start": {"line": 105, "column": 0}, "end": {"line": 105, "column": 28}}, "105": {"start": {"line": 106, "column": 0}, "end": {"line": 106, "column": 27}}, "106": {"start": {"line": 107, "column": 0}, "end": {"line": 107, "column": 9}}, "108": {"start": {"line": 109, "column": 0}, "end": {"line": 109, "column": 23}}, "109": {"start": {"line": 110, "column": 0}, "end": {"line": 110, "column": 31}}, "110": {"start": {"line": 111, "column": 0}, "end": {"line": 111, "column": 42}}, "111": {"start": {"line": 112, "column": 0}, "end": {"line": 112, "column": 30}}, "112": {"start": {"line": 113, "column": 0}, "end": {"line": 113, "column": 12}}, "113": {"start": {"line": 114, "column": 0}, "end": {"line": 114, "column": 9}}, "115": {"start": {"line": 116, "column": 0}, "end": {"line": 116, "column": 16}}, "116": {"start": {"line": 117, "column": 0}, "end": {"line": 117, "column": 28}}, "117": {"start": {"line": 118, "column": 0}, "end": {"line": 118, "column": 9}}, "118": {"start": {"line": 119, "column": 0}, "end": {"line": 119, "column": 23}}, "119": {"start": {"line": 120, "column": 0}, "end": {"line": 120, "column": 37}}, "120": {"start": {"line": 121, "column": 0}, "end": {"line": 121, "column": 51}}, "121": {"start": {"line": 122, "column": 0}, "end": {"line": 122, "column": 33}}, "122": {"start": {"line": 123, "column": 0}, "end": {"line": 123, "column": 32}}, "123": {"start": {"line": 124, "column": 0}, "end": {"line": 124, "column": 31}}, "124": {"start": {"line": 125, "column": 0}, "end": {"line": 125, "column": 14}}, "125": {"start": {"line": 126, "column": 0}, "end": {"line": 126, "column": 11}}, "126": {"start": {"line": 127, "column": 0}, "end": {"line": 127, "column": 57}}, "127": {"start": {"line": 128, "column": 0}, "end": {"line": 128, "column": 33}}, "128": {"start": {"line": 129, "column": 0}, "end": {"line": 129, "column": 34}}, "129": {"start": {"line": 130, "column": 0}, "end": {"line": 130, "column": 31}}, "130": {"start": {"line": 131, "column": 0}, "end": {"line": 131, "column": 14}}, "131": {"start": {"line": 132, "column": 0}, "end": {"line": 132, "column": 11}}, "132": {"start": {"line": 133, "column": 0}, "end": {"line": 133, "column": 9}}, "134": {"start": {"line": 135, "column": 0}, "end": {"line": 135, "column": 29}}, "135": {"start": {"line": 136, "column": 0}, "end": {"line": 136, "column": 40}}, "136": {"start": {"line": 137, "column": 0}, "end": {"line": 137, "column": 69}}, "137": {"start": {"line": 138, "column": 0}, "end": {"line": 138, "column": 10}}, "138": {"start": {"line": 139, "column": 0}, "end": {"line": 139, "column": 7}}, "139": {"start": {"line": 140, "column": 0}, "end": {"line": 140, "column": 7}}, "142": {"start": {"line": 143, "column": 0}, "end": {"line": 143, "column": 33}}, "143": {"start": {"line": 144, "column": 0}, "end": {"line": 144, "column": 29}}, "144": {"start": {"line": 145, "column": 0}, "end": {"line": 145, "column": 41}}, "145": {"start": {"line": 146, "column": 0}, "end": {"line": 146, "column": 22}}, "146": {"start": {"line": 147, "column": 0}, "end": {"line": 147, "column": 29}}, "147": {"start": {"line": 148, "column": 0}, "end": {"line": 148, "column": 31}}, "148": {"start": {"line": 149, "column": 0}, "end": {"line": 149, "column": 27}}, "149": {"start": {"line": 150, "column": 0}, "end": {"line": 150, "column": 10}}, "150": {"start": {"line": 151, "column": 0}, "end": {"line": 151, "column": 7}}, "152": {"start": {"line": 153, "column": 0}, "end": {"line": 153, "column": 11}}, "153": {"start": {"line": 154, "column": 0}, "end": {"line": 154, "column": 68}}, "154": {"start": {"line": 155, "column": 0}, "end": {"line": 155, "column": 22}}, "155": {"start": {"line": 156, "column": 0}, "end": {"line": 156, "column": 24}}, "156": {"start": {"line": 157, "column": 0}, "end": {"line": 157, "column": 9}}, "158": {"start": {"line": 159, "column": 0}, "end": {"line": 159, "column": 23}}, "159": {"start": {"line": 160, "column": 0}, "end": {"line": 160, "column": 31}}, "160": {"start": {"line": 161, "column": 0}, "end": {"line": 161, "column": 42}}, "161": {"start": {"line": 162, "column": 0}, "end": {"line": 162, "column": 30}}, "162": {"start": {"line": 163, "column": 0}, "end": {"line": 163, "column": 12}}, "163": {"start": {"line": 164, "column": 0}, "end": {"line": 164, "column": 9}}, "165": {"start": {"line": 166, "column": 0}, "end": {"line": 166, "column": 16}}, "166": {"start": {"line": 167, "column": 0}, "end": {"line": 167, "column": 28}}, "167": {"start": {"line": 168, "column": 0}, "end": {"line": 168, "column": 9}}, "168": {"start": {"line": 169, "column": 0}, "end": {"line": 169, "column": 23}}, "169": {"start": {"line": 170, "column": 0}, "end": {"line": 170, "column": 29}}, "170": {"start": {"line": 171, "column": 0}, "end": {"line": 171, "column": 40}}, "171": {"start": {"line": 172, "column": 0}, "end": {"line": 172, "column": 69}}, "172": {"start": {"line": 173, "column": 0}, "end": {"line": 173, "column": 10}}, "173": {"start": {"line": 174, "column": 0}, "end": {"line": 174, "column": 7}}, "174": {"start": {"line": 175, "column": 0}, "end": {"line": 175, "column": 7}}, "177": {"start": {"line": 178, "column": 0}, "end": {"line": 178, "column": 34}}, "178": {"start": {"line": 179, "column": 0}, "end": {"line": 179, "column": 30}}, "179": {"start": {"line": 180, "column": 0}, "end": {"line": 180, "column": 41}}, "180": {"start": {"line": 181, "column": 0}, "end": {"line": 181, "column": 22}}, "181": {"start": {"line": 182, "column": 0}, "end": {"line": 182, "column": 29}}, "182": {"start": {"line": 183, "column": 0}, "end": {"line": 183, "column": 31}}, "183": {"start": {"line": 184, "column": 0}, "end": {"line": 184, "column": 27}}, "184": {"start": {"line": 185, "column": 0}, "end": {"line": 185, "column": 10}}, "185": {"start": {"line": 186, "column": 0}, "end": {"line": 186, "column": 7}}, "187": {"start": {"line": 188, "column": 0}, "end": {"line": 188, "column": 11}}, "188": {"start": {"line": 189, "column": 0}, "end": {"line": 189, "column": 73}}, "189": {"start": {"line": 190, "column": 0}, "end": {"line": 190, "column": 22}}, "190": {"start": {"line": 191, "column": 0}, "end": {"line": 191, "column": 25}}, "191": {"start": {"line": 192, "column": 0}, "end": {"line": 192, "column": 9}}, "193": {"start": {"line": 194, "column": 0}, "end": {"line": 194, "column": 27}}, "194": {"start": {"line": 195, "column": 0}, "end": {"line": 195, "column": 31}}, "195": {"start": {"line": 196, "column": 0}, "end": {"line": 196, "column": 42}}, "196": {"start": {"line": 197, "column": 0}, "end": {"line": 197, "column": 30}}, "197": {"start": {"line": 198, "column": 0}, "end": {"line": 198, "column": 12}}, "198": {"start": {"line": 199, "column": 0}, "end": {"line": 199, "column": 9}}, "200": {"start": {"line": 201, "column": 0}, "end": {"line": 201, "column": 16}}, "201": {"start": {"line": 202, "column": 0}, "end": {"line": 202, "column": 28}}, "202": {"start": {"line": 203, "column": 0}, "end": {"line": 203, "column": 28}}, "203": {"start": {"line": 204, "column": 0}, "end": {"line": 204, "column": 9}}, "204": {"start": {"line": 205, "column": 0}, "end": {"line": 205, "column": 23}}, "205": {"start": {"line": 206, "column": 0}, "end": {"line": 206, "column": 29}}, "206": {"start": {"line": 207, "column": 0}, "end": {"line": 207, "column": 40}}, "207": {"start": {"line": 208, "column": 0}, "end": {"line": 208, "column": 69}}, "208": {"start": {"line": 209, "column": 0}, "end": {"line": 209, "column": 10}}, "209": {"start": {"line": 210, "column": 0}, "end": {"line": 210, "column": 7}}, "210": {"start": {"line": 211, "column": 0}, "end": {"line": 211, "column": 7}}, "211": {"start": {"line": 212, "column": 0}, "end": {"line": 212, "column": 2}}}, "s": {"0": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "11": 0, "12": 0, "13": 0, "14": 0, "16": 0, "17": 0, "18": 0, "20": 0, "21": 0, "22": 0, "24": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "35": 0, "36": 0, "37": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "91": 0, "92": 0, "93": 0, "94": 0, "95": 0, "96": 0, "97": 0, "98": 0, "99": 0, "101": 0, "102": 0, "103": 0, "104": 0, "105": 0, "106": 0, "108": 0, "109": 0, "110": 0, "111": 0, "112": 0, "113": 0, "115": 0, "116": 0, "117": 0, "118": 0, "119": 0, "120": 0, "121": 0, "122": 0, "123": 0, "124": 0, "125": 0, "126": 0, "127": 0, "128": 0, "129": 0, "130": 0, "131": 0, "132": 0, "134": 0, "135": 0, "136": 0, "137": 0, "138": 0, "139": 0, "142": 0, "143": 0, "144": 0, "145": 0, "146": 0, "147": 0, "148": 0, "149": 0, "150": 0, "152": 0, "153": 0, "154": 0, "155": 0, "156": 0, "158": 0, "159": 0, "160": 0, "161": 0, "162": 0, "163": 0, "165": 0, "166": 0, "167": 0, "168": 0, "169": 0, "170": 0, "171": 0, "172": 0, "173": 0, "174": 0, "177": 0, "178": 0, "179": 0, "180": 0, "181": 0, "182": 0, "183": 0, "184": 0, "185": 0, "187": 0, "188": 0, "189": 0, "190": 0, "191": 0, "193": 0, "194": 0, "195": 0, "196": 0, "197": 0, "198": 0, "200": 0, "201": 0, "202": 0, "203": 0, "204": 0, "205": 0, "206": 0, "207": 0, "208": 0, "209": 0, "210": 0, "211": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 212, "column": -662}}, "locations": [{"start": {"line": 1, "column": 0}, "end": {"line": 212, "column": -662}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 0}, "end": {"line": 212, "column": -662}}, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 212, "column": -662}}, "line": 1}}, "f": {"0": 0}}, "/Users/<USER>/code/open-trpc/packages/server-core/src/core/services/application.service.ts": {"path": "/Users/<USER>/code/open-trpc/packages/server-core/src/core/services/application.service.ts", "all": true, "statementMap": {"4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 33}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 14}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 57}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 40}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 6}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 26}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 19}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 17}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 25}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 23}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 60}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 21}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 20}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 26}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 8}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 48}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 16}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 35}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 50}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 7}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 67}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 57}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 68}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 61}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 48}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 13}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 12}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 11}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 18}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 27}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 17}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 20}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 23}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 5}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 80}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 34}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 3}}, "58": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 69}}, "59": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 58}}, "60": {"start": {"line": 61, "column": 0}, "end": {"line": 61, "column": 3}}, "65": {"start": {"line": 66, "column": 0}, "end": {"line": 66, "column": 75}}, "66": {"start": {"line": 67, "column": 0}, "end": {"line": 67, "column": 56}}, "67": {"start": {"line": 68, "column": 0}, "end": {"line": 68, "column": 3}}, "72": {"start": {"line": 73, "column": 0}, "end": {"line": 73, "column": 69}}, "73": {"start": {"line": 74, "column": 0}, "end": {"line": 74, "column": 50}}, "74": {"start": {"line": 75, "column": 0}, "end": {"line": 75, "column": 3}}, "79": {"start": {"line": 80, "column": 0}, "end": {"line": 80, "column": 93}}, "80": {"start": {"line": 81, "column": 0}, "end": {"line": 81, "column": 75}}, "81": {"start": {"line": 82, "column": 0}, "end": {"line": 82, "column": 23}}, "82": {"start": {"line": 83, "column": 0}, "end": {"line": 83, "column": 17}}, "83": {"start": {"line": 84, "column": 0}, "end": {"line": 84, "column": 5}}, "85": {"start": {"line": 86, "column": 0}, "end": {"line": 86, "column": 91}}, "86": {"start": {"line": 87, "column": 0}, "end": {"line": 87, "column": 25}}, "87": {"start": {"line": 88, "column": 0}, "end": {"line": 88, "column": 17}}, "88": {"start": {"line": 89, "column": 0}, "end": {"line": 89, "column": 5}}, "90": {"start": {"line": 91, "column": 0}, "end": {"line": 91, "column": 22}}, "91": {"start": {"line": 92, "column": 0}, "end": {"line": 92, "column": 3}}, "96": {"start": {"line": 97, "column": 0}, "end": {"line": 97, "column": 26}}, "97": {"start": {"line": 98, "column": 0}, "end": {"line": 98, "column": 15}}, "98": {"start": {"line": 99, "column": 0}, "end": {"line": 99, "column": 19}}, "99": {"start": {"line": 100, "column": 0}, "end": {"line": 100, "column": 95}}, "100": {"start": {"line": 101, "column": 0}, "end": {"line": 101, "column": 34}}, "102": {"start": {"line": 103, "column": 0}, "end": {"line": 103, "column": 69}}, "103": {"start": {"line": 104, "column": 0}, "end": {"line": 104, "column": 56}}, "104": {"start": {"line": 105, "column": 0}, "end": {"line": 105, "column": 46}}, "105": {"start": {"line": 106, "column": 0}, "end": {"line": 106, "column": 5}}, "107": {"start": {"line": 108, "column": 0}, "end": {"line": 108, "column": 57}}, "108": {"start": {"line": 109, "column": 0}, "end": {"line": 109, "column": 3}}, "113": {"start": {"line": 114, "column": 0}, "end": {"line": 114, "column": 109}}, "115": {"start": {"line": 116, "column": 0}, "end": {"line": 116, "column": 69}}, "116": {"start": {"line": 117, "column": 0}, "end": {"line": 117, "column": 56}}, "117": {"start": {"line": 118, "column": 0}, "end": {"line": 118, "column": 46}}, "118": {"start": {"line": 119, "column": 0}, "end": {"line": 119, "column": 5}}, "121": {"start": {"line": 122, "column": 0}, "end": {"line": 122, "column": 60}}, "122": {"start": {"line": 123, "column": 0}, "end": {"line": 123, "column": 71}}, "124": {"start": {"line": 125, "column": 0}, "end": {"line": 125, "column": 76}}, "125": {"start": {"line": 126, "column": 0}, "end": {"line": 126, "column": 27}}, "126": {"start": {"line": 127, "column": 0}, "end": {"line": 127, "column": 6}}, "128": {"start": {"line": 129, "column": 0}, "end": {"line": 129, "column": 30}}, "129": {"start": {"line": 130, "column": 0}, "end": {"line": 130, "column": 51}}, "130": {"start": {"line": 131, "column": 0}, "end": {"line": 131, "column": 5}}, "132": {"start": {"line": 133, "column": 0}, "end": {"line": 133, "column": 65}}, "133": {"start": {"line": 134, "column": 0}, "end": {"line": 134, "column": 3}}, "138": {"start": {"line": 139, "column": 0}, "end": {"line": 139, "column": 73}}, "140": {"start": {"line": 141, "column": 0}, "end": {"line": 141, "column": 69}}, "141": {"start": {"line": 142, "column": 0}, "end": {"line": 142, "column": 56}}, "142": {"start": {"line": 143, "column": 0}, "end": {"line": 143, "column": 46}}, "143": {"start": {"line": 144, "column": 0}, "end": {"line": 144, "column": 5}}, "145": {"start": {"line": 146, "column": 0}, "end": {"line": 146, "column": 48}}, "146": {"start": {"line": 147, "column": 0}, "end": {"line": 147, "column": 3}}, "151": {"start": {"line": 152, "column": 0}, "end": {"line": 152, "column": 57}}, "152": {"start": {"line": 153, "column": 0}, "end": {"line": 153, "column": 48}}, "153": {"start": {"line": 154, "column": 0}, "end": {"line": 154, "column": 3}}, "158": {"start": {"line": 159, "column": 0}, "end": {"line": 159, "column": 54}}, "159": {"start": {"line": 160, "column": 0}, "end": {"line": 160, "column": 58}}, "160": {"start": {"line": 161, "column": 0}, "end": {"line": 161, "column": 3}}, "161": {"start": {"line": 162, "column": 0}, "end": {"line": 162, "column": 1}}}, "s": {"4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "20": 0, "21": 0, "22": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "33": 0, "34": 0, "37": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "50": 0, "52": 0, "53": 0, "58": 0, "59": 0, "60": 0, "65": 0, "66": 0, "67": 0, "72": 0, "73": 0, "74": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "85": 0, "86": 0, "87": 0, "88": 0, "90": 0, "91": 0, "96": 0, "97": 0, "98": 0, "99": 0, "100": 0, "102": 0, "103": 0, "104": 0, "105": 0, "107": 0, "108": 0, "113": 0, "115": 0, "116": 0, "117": 0, "118": 0, "121": 0, "122": 0, "124": 0, "125": 0, "126": 0, "128": 0, "129": 0, "130": 0, "132": 0, "133": 0, "138": 0, "140": 0, "141": 0, "142": 0, "143": 0, "145": 0, "146": 0, "151": 0, "152": 0, "153": 0, "158": 0, "159": 0, "160": 0, "161": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 4217}, "end": {"line": 162, "column": 1}}, "locations": [{"start": {"line": 1, "column": 4217}, "end": {"line": 162, "column": 1}}]}}, "b": {"0": [1]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 4217}, "end": {"line": 162, "column": 1}}, "loc": {"start": {"line": 1, "column": 4217}, "end": {"line": 162, "column": 1}}, "line": 1}}, "f": {"0": 1}}, "/Users/<USER>/code/open-trpc/packages/server-core/src/core/services/auth.service.ts": {"path": "/Users/<USER>/code/open-trpc/packages/server-core/src/core/services/auth.service.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 29}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 26}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 14}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 43}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 53}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 34}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 6}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 53}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 29}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 50}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 97}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 3}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 57}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 36}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 3}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 74}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 41}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 3}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 20}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 18}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 59}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 50}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 73}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 29}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 58}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 5}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 73}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 49}}, "57": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 50}}, "58": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 36}}, "59": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 5}}, "62": {"start": {"line": 63, "column": 0}, "end": {"line": 63, "column": 9}}, "64": {"start": {"line": 65, "column": 0}, "end": {"line": 65, "column": 40}}, "65": {"start": {"line": 66, "column": 0}, "end": {"line": 66, "column": 21}}, "66": {"start": {"line": 67, "column": 0}, "end": {"line": 67, "column": 37}}, "67": {"start": {"line": 68, "column": 0}, "end": {"line": 68, "column": 41}}, "68": {"start": {"line": 69, "column": 0}, "end": {"line": 69, "column": 5}}, "69": {"start": {"line": 70, "column": 0}, "end": {"line": 70, "column": 3}}, "74": {"start": {"line": 75, "column": 0}, "end": {"line": 75, "column": 22}}, "75": {"start": {"line": 76, "column": 0}, "end": {"line": 76, "column": 18}}, "76": {"start": {"line": 77, "column": 0}, "end": {"line": 77, "column": 17}}, "77": {"start": {"line": 78, "column": 0}, "end": {"line": 78, "column": 59}}, "78": {"start": {"line": 79, "column": 0}, "end": {"line": 79, "column": 23}}, "79": {"start": {"line": 80, "column": 0}, "end": {"line": 80, "column": 79}}, "80": {"start": {"line": 81, "column": 0}, "end": {"line": 81, "column": 25}}, "81": {"start": {"line": 82, "column": 0}, "end": {"line": 82, "column": 3}}, "86": {"start": {"line": 87, "column": 0}, "end": {"line": 87, "column": 33}}, "87": {"start": {"line": 88, "column": 0}, "end": {"line": 88, "column": 18}}, "88": {"start": {"line": 89, "column": 0}, "end": {"line": 89, "column": 16}}, "89": {"start": {"line": 90, "column": 0}, "end": {"line": 90, "column": 69}}, "91": {"start": {"line": 92, "column": 0}, "end": {"line": 92, "column": 70}}, "92": {"start": {"line": 93, "column": 0}, "end": {"line": 93, "column": 23}}, "93": {"start": {"line": 94, "column": 0}, "end": {"line": 94, "column": 34}}, "94": {"start": {"line": 95, "column": 0}, "end": {"line": 95, "column": 5}}, "97": {"start": {"line": 98, "column": 0}, "end": {"line": 98, "column": 59}}, "98": {"start": {"line": 99, "column": 0}, "end": {"line": 99, "column": 25}}, "100": {"start": {"line": 101, "column": 0}, "end": {"line": 101, "column": 16}}, "102": {"start": {"line": 103, "column": 0}, "end": {"line": 103, "column": 36}}, "103": {"start": {"line": 104, "column": 0}, "end": {"line": 104, "column": 14}}, "104": {"start": {"line": 105, "column": 0}, "end": {"line": 105, "column": 28}}, "105": {"start": {"line": 106, "column": 0}, "end": {"line": 106, "column": 46}}, "106": {"start": {"line": 107, "column": 0}, "end": {"line": 107, "column": 34}}, "107": {"start": {"line": 108, "column": 0}, "end": {"line": 108, "column": 7}}, "109": {"start": {"line": 110, "column": 0}, "end": {"line": 110, "column": 55}}, "110": {"start": {"line": 111, "column": 0}, "end": {"line": 111, "column": 22}}, "111": {"start": {"line": 112, "column": 0}, "end": {"line": 112, "column": 5}}, "114": {"start": {"line": 115, "column": 0}, "end": {"line": 115, "column": 45}}, "117": {"start": {"line": 118, "column": 0}, "end": {"line": 118, "column": 49}}, "118": {"start": {"line": 119, "column": 0}, "end": {"line": 119, "column": 22}}, "119": {"start": {"line": 120, "column": 0}, "end": {"line": 120, "column": 30}}, "120": {"start": {"line": 121, "column": 0}, "end": {"line": 121, "column": 6}}, "122": {"start": {"line": 123, "column": 0}, "end": {"line": 123, "column": 47}}, "123": {"start": {"line": 124, "column": 0}, "end": {"line": 124, "column": 3}}, "128": {"start": {"line": 129, "column": 0}, "end": {"line": 129, "column": 26}}, "129": {"start": {"line": 130, "column": 0}, "end": {"line": 130, "column": 25}}, "130": {"start": {"line": 131, "column": 0}, "end": {"line": 131, "column": 20}}, "131": {"start": {"line": 132, "column": 0}, "end": {"line": 132, "column": 49}}, "133": {"start": {"line": 134, "column": 0}, "end": {"line": 134, "column": 46}}, "135": {"start": {"line": 136, "column": 0}, "end": {"line": 136, "column": 25}}, "136": {"start": {"line": 137, "column": 0}, "end": {"line": 137, "column": 59}}, "137": {"start": {"line": 138, "column": 0}, "end": {"line": 138, "column": 59}}, "139": {"start": {"line": 140, "column": 0}, "end": {"line": 140, "column": 16}}, "140": {"start": {"line": 141, "column": 0}, "end": {"line": 141, "column": 30}}, "141": {"start": {"line": 142, "column": 0}, "end": {"line": 142, "column": 5}}, "143": {"start": {"line": 144, "column": 0}, "end": {"line": 144, "column": 25}}, "144": {"start": {"line": 145, "column": 0}, "end": {"line": 145, "column": 45}}, "145": {"start": {"line": 146, "column": 0}, "end": {"line": 146, "column": 5}}, "148": {"start": {"line": 149, "column": 0}, "end": {"line": 149, "column": 78}}, "149": {"start": {"line": 150, "column": 0}, "end": {"line": 150, "column": 27}}, "150": {"start": {"line": 151, "column": 0}, "end": {"line": 151, "column": 29}}, "151": {"start": {"line": 152, "column": 0}, "end": {"line": 152, "column": 5}}, "154": {"start": {"line": 155, "column": 0}, "end": {"line": 155, "column": 45}}, "157": {"start": {"line": 158, "column": 0}, "end": {"line": 158, "column": 49}}, "158": {"start": {"line": 159, "column": 0}, "end": {"line": 159, "column": 22}}, "159": {"start": {"line": 160, "column": 0}, "end": {"line": 160, "column": 30}}, "160": {"start": {"line": 161, "column": 0}, "end": {"line": 161, "column": 6}}, "162": {"start": {"line": 163, "column": 0}, "end": {"line": 163, "column": 36}}, "163": {"start": {"line": 164, "column": 0}, "end": {"line": 164, "column": 3}}, "164": {"start": {"line": 165, "column": 0}, "end": {"line": 165, "column": 1}}}, "s": {"0": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "28": 0, "29": 0, "30": 0, "35": 0, "36": 0, "37": 0, "42": 0, "43": 0, "44": 0, "45": 0, "47": 0, "48": 0, "49": 0, "50": 0, "53": 0, "56": 0, "57": 0, "58": 0, "59": 0, "62": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "86": 0, "87": 0, "88": 0, "89": 0, "91": 0, "92": 0, "93": 0, "94": 0, "97": 0, "98": 0, "100": 0, "102": 0, "103": 0, "104": 0, "105": 0, "106": 0, "107": 0, "109": 0, "110": 0, "111": 0, "114": 0, "117": 0, "118": 0, "119": 0, "120": 0, "122": 0, "123": 0, "128": 0, "129": 0, "130": 0, "131": 0, "133": 0, "135": 0, "136": 0, "137": 0, "139": 0, "140": 0, "141": 0, "143": 0, "144": 0, "145": 0, "148": 0, "149": 0, "150": 0, "151": 0, "154": 0, "157": 0, "158": 0, "159": 0, "160": 0, "162": 0, "163": 0, "164": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 4030}, "end": {"line": 165, "column": 1}}, "locations": [{"start": {"line": 1, "column": 4030}, "end": {"line": 165, "column": 1}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 4030}, "end": {"line": 165, "column": 1}}, "loc": {"start": {"line": 1, "column": 4030}, "end": {"line": 165, "column": 1}}, "line": 1}}, "f": {"0": 0}}, "/Users/<USER>/code/open-trpc/packages/server-core/src/core/services/balance.service.ts": {"path": "/Users/<USER>/code/open-trpc/packages/server-core/src/core/services/balance.service.ts", "all": true, "statementMap": {"3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 29}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 62}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 71}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 83}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 19}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 43}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 22}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 24}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 8}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 19}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 5}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 26}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 3}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 89}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 70}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 3}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 24}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 26}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 18}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 34}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 74}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 71}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 91}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 26}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 98}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 28}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 48}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 7}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 27}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 12}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 44}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 22}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 28}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 8}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 5}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 3}}, "61": {"start": {"line": 62, "column": 0}, "end": {"line": 62, "column": 22}}, "62": {"start": {"line": 63, "column": 0}, "end": {"line": 63, "column": 26}}, "63": {"start": {"line": 64, "column": 0}, "end": {"line": 64, "column": 18}}, "64": {"start": {"line": 65, "column": 0}, "end": {"line": 65, "column": 34}}, "65": {"start": {"line": 66, "column": 0}, "end": {"line": 66, "column": 74}}, "66": {"start": {"line": 67, "column": 0}, "end": {"line": 67, "column": 52}}, "68": {"start": {"line": 69, "column": 0}, "end": {"line": 69, "column": 33}}, "69": {"start": {"line": 70, "column": 0}, "end": {"line": 70, "column": 45}}, "70": {"start": {"line": 71, "column": 0}, "end": {"line": 71, "column": 5}}, "72": {"start": {"line": 73, "column": 0}, "end": {"line": 73, "column": 58}}, "73": {"start": {"line": 74, "column": 0}, "end": {"line": 74, "column": 96}}, "75": {"start": {"line": 76, "column": 0}, "end": {"line": 76, "column": 26}}, "76": {"start": {"line": 77, "column": 0}, "end": {"line": 77, "column": 46}}, "77": {"start": {"line": 78, "column": 0}, "end": {"line": 78, "column": 5}}, "79": {"start": {"line": 80, "column": 0}, "end": {"line": 80, "column": 25}}, "80": {"start": {"line": 81, "column": 0}, "end": {"line": 81, "column": 3}}, "85": {"start": {"line": 86, "column": 0}, "end": {"line": 86, "column": 19}}, "86": {"start": {"line": 87, "column": 0}, "end": {"line": 87, "column": 26}}, "87": {"start": {"line": 88, "column": 0}, "end": {"line": 88, "column": 19}}, "88": {"start": {"line": 89, "column": 0}, "end": {"line": 89, "column": 34}}, "89": {"start": {"line": 90, "column": 0}, "end": {"line": 90, "column": 91}}, "91": {"start": {"line": 92, "column": 0}, "end": {"line": 92, "column": 26}}, "92": {"start": {"line": 93, "column": 0}, "end": {"line": 93, "column": 95}}, "93": {"start": {"line": 94, "column": 0}, "end": {"line": 94, "column": 28}}, "94": {"start": {"line": 95, "column": 0}, "end": {"line": 95, "column": 48}}, "95": {"start": {"line": 96, "column": 0}, "end": {"line": 96, "column": 7}}, "96": {"start": {"line": 97, "column": 0}, "end": {"line": 97, "column": 27}}, "97": {"start": {"line": 98, "column": 0}, "end": {"line": 98, "column": 12}}, "98": {"start": {"line": 99, "column": 0}, "end": {"line": 99, "column": 44}}, "99": {"start": {"line": 100, "column": 0}, "end": {"line": 100, "column": 22}}, "100": {"start": {"line": 101, "column": 0}, "end": {"line": 101, "column": 16}}, "101": {"start": {"line": 102, "column": 0}, "end": {"line": 102, "column": 8}}, "102": {"start": {"line": 103, "column": 0}, "end": {"line": 103, "column": 5}}, "103": {"start": {"line": 104, "column": 0}, "end": {"line": 104, "column": 3}}, "108": {"start": {"line": 109, "column": 0}, "end": {"line": 109, "column": 25}}, "109": {"start": {"line": 110, "column": 0}, "end": {"line": 110, "column": 26}}, "110": {"start": {"line": 111, "column": 0}, "end": {"line": 111, "column": 26}}, "111": {"start": {"line": 112, "column": 0}, "end": {"line": 112, "column": 23}}, "112": {"start": {"line": 113, "column": 0}, "end": {"line": 113, "column": 74}}, "113": {"start": {"line": 114, "column": 0}, "end": {"line": 114, "column": 55}}, "114": {"start": {"line": 115, "column": 0}, "end": {"line": 115, "column": 3}}, "115": {"start": {"line": 116, "column": 0}, "end": {"line": 116, "column": 1}}}, "s": {"3": 0, "4": 0, "9": 0, "10": 0, "12": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "21": 0, "22": 0, "27": 0, "28": 0, "29": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "42": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "68": 0, "69": 0, "70": 0, "72": 0, "73": 0, "75": 0, "76": 0, "77": 0, "79": 0, "80": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "91": 0, "92": 0, "93": 0, "94": 0, "95": 0, "96": 0, "97": 0, "98": 0, "99": 0, "100": 0, "101": 0, "102": 0, "103": 0, "108": 0, "109": 0, "110": 0, "111": 0, "112": 0, "113": 0, "114": 0, "115": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 3055}, "end": {"line": 116, "column": 1}}, "locations": [{"start": {"line": 1, "column": 3055}, "end": {"line": 116, "column": 1}}]}}, "b": {"0": [1]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 3055}, "end": {"line": 116, "column": 1}}, "loc": {"start": {"line": 1, "column": 3055}, "end": {"line": 116, "column": 1}}, "line": 1}}, "f": {"0": 1}}, "/Users/<USER>/code/open-trpc/packages/server-core/src/core/services/crypto.service.ts": {"path": "/Users/<USER>/code/open-trpc/packages/server-core/src/core/services/crypto.service.ts", "all": false, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 29}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 22}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 28}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 28}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 68}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 3}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 53}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 43}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 3}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 78}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 47}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 3}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 50}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 29}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 19}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 5}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 36}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 32}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 48}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 36}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 3}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 27}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 68}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 32}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 3}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 31}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 69}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 31}}, "57": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 3}}, "62": {"start": {"line": 63, "column": 0}, "end": {"line": 63, "column": 34}}, "63": {"start": {"line": 64, "column": 0}, "end": {"line": 64, "column": 52}}, "64": {"start": {"line": 65, "column": 0}, "end": {"line": 65, "column": 3}}, "69": {"start": {"line": 70, "column": 0}, "end": {"line": 70, "column": 29}}, "70": {"start": {"line": 71, "column": 0}, "end": {"line": 71, "column": 32}}, "71": {"start": {"line": 72, "column": 0}, "end": {"line": 72, "column": 63}}, "72": {"start": {"line": 73, "column": 0}, "end": {"line": 73, "column": 37}}, "73": {"start": {"line": 74, "column": 0}, "end": {"line": 74, "column": 3}}, "74": {"start": {"line": 75, "column": 0}, "end": {"line": 75, "column": 1}}}, "s": {"0": 1, "3": 1, "5": 1, "9": 1, "11": 2, "12": 2, "17": 1, "18": 1, "19": 1, "24": 1, "25": 2, "26": 2, "31": 1, "32": 3, "33": 2, "34": 2, "35": 1, "36": 1, "37": 1, "38": 1, "39": 3, "45": 1, "46": 3, "47": 3, "48": 3, "54": 1, "55": 3, "56": 3, "57": 3, "62": 1, "63": 3, "64": 3, "69": 1, "70": 3, "71": 3, "72": 3, "73": 3, "74": 1}, "branchMap": {"0": {"type": "branch", "line": 10, "loc": {"start": {"line": 10, "column": 2}, "end": {"line": 13, "column": 3}}, "locations": [{"start": {"line": 10, "column": 2}, "end": {"line": 13, "column": 3}}]}, "1": {"type": "branch", "line": 18, "loc": {"start": {"line": 18, "column": 2}, "end": {"line": 20, "column": 3}}, "locations": [{"start": {"line": 18, "column": 2}, "end": {"line": 20, "column": 3}}]}, "2": {"type": "branch", "line": 25, "loc": {"start": {"line": 25, "column": 2}, "end": {"line": 27, "column": 3}}, "locations": [{"start": {"line": 25, "column": 2}, "end": {"line": 27, "column": 3}}]}, "3": {"type": "branch", "line": 32, "loc": {"start": {"line": 32, "column": 2}, "end": {"line": 40, "column": 3}}, "locations": [{"start": {"line": 32, "column": 2}, "end": {"line": 40, "column": 3}}]}, "4": {"type": "branch", "line": 33, "loc": {"start": {"line": 33, "column": 28}, "end": {"line": 35, "column": 5}}, "locations": [{"start": {"line": 33, "column": 28}, "end": {"line": 35, "column": 5}}]}, "5": {"type": "branch", "line": 35, "loc": {"start": {"line": 35, "column": 4}, "end": {"line": 39, "column": 36}}, "locations": [{"start": {"line": 35, "column": 4}, "end": {"line": 39, "column": 36}}]}, "6": {"type": "branch", "line": 46, "loc": {"start": {"line": 46, "column": 2}, "end": {"line": 49, "column": 3}}, "locations": [{"start": {"line": 46, "column": 2}, "end": {"line": 49, "column": 3}}]}, "7": {"type": "branch", "line": 55, "loc": {"start": {"line": 55, "column": 2}, "end": {"line": 58, "column": 3}}, "locations": [{"start": {"line": 55, "column": 2}, "end": {"line": 58, "column": 3}}]}, "8": {"type": "branch", "line": 63, "loc": {"start": {"line": 63, "column": 2}, "end": {"line": 65, "column": 3}}, "locations": [{"start": {"line": 63, "column": 2}, "end": {"line": 65, "column": 3}}]}, "9": {"type": "branch", "line": 70, "loc": {"start": {"line": 70, "column": 2}, "end": {"line": 74, "column": 3}}, "locations": [{"start": {"line": 70, "column": 2}, "end": {"line": 74, "column": 3}}]}}, "b": {"0": [2], "1": [1], "2": [2], "3": [3], "4": [2], "5": [1], "6": [3], "7": [3], "8": [3], "9": [3]}, "fnMap": {"0": {"name": "generateSecret", "decl": {"start": {"line": 10, "column": 2}, "end": {"line": 13, "column": 3}}, "loc": {"start": {"line": 10, "column": 2}, "end": {"line": 13, "column": 3}}, "line": 10}, "1": {"name": "hashSecret", "decl": {"start": {"line": 18, "column": 2}, "end": {"line": 20, "column": 3}}, "loc": {"start": {"line": 18, "column": 2}, "end": {"line": 20, "column": 3}}, "line": 18}, "2": {"name": "verifySecret", "decl": {"start": {"line": 25, "column": 2}, "end": {"line": 27, "column": 3}}, "loc": {"start": {"line": 25, "column": 2}, "end": {"line": 27, "column": 3}}, "line": 25}, "3": {"name": "formatSecretForDisplay", "decl": {"start": {"line": 32, "column": 2}, "end": {"line": 40, "column": 3}}, "loc": {"start": {"line": 32, "column": 2}, "end": {"line": 40, "column": 3}}, "line": 32}, "4": {"name": "generateAppId", "decl": {"start": {"line": 46, "column": 2}, "end": {"line": 49, "column": 3}}, "loc": {"start": {"line": 46, "column": 2}, "end": {"line": 49, "column": 3}}, "line": 46}, "5": {"name": "generateAppSecret", "decl": {"start": {"line": 55, "column": 2}, "end": {"line": 58, "column": 3}}, "loc": {"start": {"line": 55, "column": 2}, "end": {"line": 58, "column": 3}}, "line": 55}, "6": {"name": "generateSessionToken", "decl": {"start": {"line": 63, "column": 2}, "end": {"line": 65, "column": 3}}, "loc": {"start": {"line": 63, "column": 2}, "end": {"line": 65, "column": 3}}, "line": 63}, "7": {"name": "generateOrderNo", "decl": {"start": {"line": 70, "column": 2}, "end": {"line": 74, "column": 3}}, "loc": {"start": {"line": 70, "column": 2}, "end": {"line": 74, "column": 3}}, "line": 70}}, "f": {"0": 2, "1": 1, "2": 2, "3": 3, "4": 3, "5": 3, "6": 3, "7": 3}}, "/Users/<USER>/code/open-trpc/packages/server-core/src/core/services/index.ts": {"path": "/Users/<USER>/code/open-trpc/packages/server-core/src/core/services/index.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 59}}}, "s": {"0": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 609}, "end": {"line": 9, "column": 84}}, "locations": [{"start": {"line": 1, "column": 609}, "end": {"line": 9, "column": 84}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 609}, "end": {"line": 9, "column": 84}}, "loc": {"start": {"line": 1, "column": 609}, "end": {"line": 9, "column": 84}}, "line": 1}}, "f": {"0": 0}}, "/Users/<USER>/code/open-trpc/packages/server-core/src/core/services/jwt.service.ts": {"path": "/Users/<USER>/code/open-trpc/packages/server-core/src/core/services/jwt.service.ts", "all": false, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 30}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 29}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 33}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 25}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 43}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 46}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 46}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 32}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 6}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 3}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 60}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 46}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 37}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 6}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 3}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 42}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 9}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 60}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 21}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 38}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 5}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 3}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 56}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 9}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 76}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 40}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 45}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 7}}, "57": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 20}}, "58": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 21}}, "59": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 38}}, "60": {"start": {"line": 61, "column": 0}, "end": {"line": 61, "column": 5}}, "61": {"start": {"line": 62, "column": 0}, "end": {"line": 62, "column": 3}}, "66": {"start": {"line": 67, "column": 0}, "end": {"line": 67, "column": 35}}, "67": {"start": {"line": 68, "column": 0}, "end": {"line": 68, "column": 28}}, "68": {"start": {"line": 69, "column": 0}, "end": {"line": 69, "column": 3}}, "69": {"start": {"line": 70, "column": 0}, "end": {"line": 70, "column": 1}}}, "s": {"0": 1, "2": 1, "3": 1, "16": 1, "17": 1, "22": 1, "23": 4, "24": 4, "25": 4, "26": 4, "31": 1, "32": 2, "33": 2, "34": 2, "35": 2, "40": 1, "41": 3, "42": 3, "43": 3, "44": 2, "45": 2, "46": 3, "51": 1, "52": 4, "53": 4, "54": 4, "55": 2, "56": 2, "57": 1, "58": 4, "59": 3, "60": 3, "61": 4, "66": 1, "67": 2, "68": 2, "69": 1}, "branchMap": {"0": {"type": "branch", "line": 18, "loc": {"start": {"line": 18, "column": 2}, "end": {"line": 18, "column": 43}}, "locations": [{"start": {"line": 18, "column": 2}, "end": {"line": 18, "column": 43}}]}, "1": {"type": "branch", "line": 23, "loc": {"start": {"line": 23, "column": 2}, "end": {"line": 27, "column": 3}}, "locations": [{"start": {"line": 23, "column": 2}, "end": {"line": 27, "column": 3}}]}, "2": {"type": "branch", "line": 32, "loc": {"start": {"line": 32, "column": 2}, "end": {"line": 36, "column": 3}}, "locations": [{"start": {"line": 32, "column": 2}, "end": {"line": 36, "column": 3}}]}, "3": {"type": "branch", "line": 41, "loc": {"start": {"line": 41, "column": 2}, "end": {"line": 47, "column": 3}}, "locations": [{"start": {"line": 41, "column": 2}, "end": {"line": 47, "column": 3}}]}, "4": {"type": "branch", "line": 44, "loc": {"start": {"line": 44, "column": 4}, "end": {"line": 46, "column": 5}}, "locations": [{"start": {"line": 44, "column": 4}, "end": {"line": 46, "column": 5}}]}, "5": {"type": "branch", "line": 52, "loc": {"start": {"line": 52, "column": 2}, "end": {"line": 62, "column": 3}}, "locations": [{"start": {"line": 52, "column": 2}, "end": {"line": 62, "column": 3}}]}, "6": {"type": "branch", "line": 55, "loc": {"start": {"line": 55, "column": 39}, "end": {"line": 57, "column": 7}}, "locations": [{"start": {"line": 55, "column": 39}, "end": {"line": 57, "column": 7}}]}, "7": {"type": "branch", "line": 57, "loc": {"start": {"line": 57, "column": 6}, "end": {"line": 59, "column": 13}}, "locations": [{"start": {"line": 57, "column": 6}, "end": {"line": 59, "column": 13}}]}, "8": {"type": "branch", "line": 59, "loc": {"start": {"line": 59, "column": 4}, "end": {"line": 61, "column": 5}}, "locations": [{"start": {"line": 59, "column": 4}, "end": {"line": 61, "column": 5}}]}, "9": {"type": "branch", "line": 67, "loc": {"start": {"line": 67, "column": 2}, "end": {"line": 69, "column": 3}}, "locations": [{"start": {"line": 67, "column": 2}, "end": {"line": 69, "column": 3}}]}}, "b": {"0": [10], "1": [4], "2": [2], "3": [3], "4": [2], "5": [4], "6": [2], "7": [1], "8": [3], "9": [2]}, "fnMap": {"0": {"name": "JwtService", "decl": {"start": {"line": 18, "column": 2}, "end": {"line": 18, "column": 43}}, "loc": {"start": {"line": 18, "column": 2}, "end": {"line": 18, "column": 43}}, "line": 18}, "1": {"name": "generateToken", "decl": {"start": {"line": 23, "column": 2}, "end": {"line": 27, "column": 3}}, "loc": {"start": {"line": 23, "column": 2}, "end": {"line": 27, "column": 3}}, "line": 23}, "2": {"name": "generateOpenAPIToken", "decl": {"start": {"line": 32, "column": 2}, "end": {"line": 36, "column": 3}}, "loc": {"start": {"line": 32, "column": 2}, "end": {"line": 36, "column": 3}}, "line": 32}, "3": {"name": "verifyToken", "decl": {"start": {"line": 41, "column": 2}, "end": {"line": 47, "column": 3}}, "loc": {"start": {"line": 41, "column": 2}, "end": {"line": 47, "column": 3}}, "line": 41}, "4": {"name": "verifyOpenAPIToken", "decl": {"start": {"line": 52, "column": 2}, "end": {"line": 62, "column": 3}}, "loc": {"start": {"line": 52, "column": 2}, "end": {"line": 62, "column": 3}}, "line": 52}, "5": {"name": "decodeToken", "decl": {"start": {"line": 67, "column": 2}, "end": {"line": 69, "column": 3}}, "loc": {"start": {"line": 67, "column": 2}, "end": {"line": 69, "column": 3}}, "line": 67}}, "f": {"0": 10, "1": 4, "2": 2, "3": 3, "4": 4, "5": 2}}, "/Users/<USER>/code/open-trpc/packages/server-core/src/core/services/order.service.ts": {"path": "/Users/<USER>/code/open-trpc/packages/server-core/src/core/services/order.service.ts", "all": true, "statementMap": {"25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 27}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 14}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 45}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 43}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 40}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 6}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 73}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 76}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 56}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 39}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 36}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 14}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 13}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 20}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 36}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 32}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 11}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 34}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 14}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 5}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 62}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 70}}, "58": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 18}}, "59": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 3}}, "64": {"start": {"line": 65, "column": 0}, "end": {"line": 65, "column": 47}}, "65": {"start": {"line": 66, "column": 0}, "end": {"line": 66, "column": 69}}, "67": {"start": {"line": 68, "column": 0}, "end": {"line": 68, "column": 23}}, "69": {"start": {"line": 70, "column": 0}, "end": {"line": 70, "column": 34}}, "70": {"start": {"line": 71, "column": 0}, "end": {"line": 71, "column": 95}}, "71": {"start": {"line": 72, "column": 0}, "end": {"line": 72, "column": 24}}, "72": {"start": {"line": 73, "column": 0}, "end": {"line": 73, "column": 72}}, "73": {"start": {"line": 74, "column": 0}, "end": {"line": 74, "column": 31}}, "74": {"start": {"line": 75, "column": 0}, "end": {"line": 75, "column": 86}}, "75": {"start": {"line": 76, "column": 0}, "end": {"line": 76, "column": 12}}, "77": {"start": {"line": 78, "column": 0}, "end": {"line": 78, "column": 17}}, "78": {"start": {"line": 79, "column": 0}, "end": {"line": 79, "column": 5}}, "81": {"start": {"line": 82, "column": 0}, "end": {"line": 82, "column": 44}}, "82": {"start": {"line": 83, "column": 0}, "end": {"line": 83, "column": 75}}, "84": {"start": {"line": 85, "column": 0}, "end": {"line": 85, "column": 12}}, "85": {"start": {"line": 86, "column": 0}, "end": {"line": 86, "column": 28}}, "86": {"start": {"line": 87, "column": 0}, "end": {"line": 87, "column": 27}}, "87": {"start": {"line": 88, "column": 0}, "end": {"line": 88, "column": 11}}, "88": {"start": {"line": 89, "column": 0}, "end": {"line": 89, "column": 15}}, "89": {"start": {"line": 90, "column": 0}, "end": {"line": 90, "column": 5}}, "90": {"start": {"line": 91, "column": 0}, "end": {"line": 91, "column": 3}}, "95": {"start": {"line": 96, "column": 0}, "end": {"line": 96, "column": 62}}, "96": {"start": {"line": 97, "column": 0}, "end": {"line": 97, "column": 49}}, "97": {"start": {"line": 98, "column": 0}, "end": {"line": 98, "column": 3}}, "102": {"start": {"line": 103, "column": 0}, "end": {"line": 103, "column": 67}}, "103": {"start": {"line": 104, "column": 0}, "end": {"line": 104, "column": 54}}, "104": {"start": {"line": 105, "column": 0}, "end": {"line": 105, "column": 3}}, "109": {"start": {"line": 110, "column": 0}, "end": {"line": 110, "column": 26}}, "110": {"start": {"line": 111, "column": 0}, "end": {"line": 111, "column": 20}}, "111": {"start": {"line": 112, "column": 0}, "end": {"line": 112, "column": 19}}, "112": {"start": {"line": 113, "column": 0}, "end": {"line": 113, "column": 20}}, "113": {"start": {"line": 114, "column": 0}, "end": {"line": 114, "column": 28}}, "114": {"start": {"line": 115, "column": 0}, "end": {"line": 115, "column": 55}}, "115": {"start": {"line": 116, "column": 0}, "end": {"line": 116, "column": 18}}, "116": {"start": {"line": 117, "column": 0}, "end": {"line": 117, "column": 34}}, "117": {"start": {"line": 118, "column": 0}, "end": {"line": 118, "column": 5}}, "119": {"start": {"line": 120, "column": 0}, "end": {"line": 120, "column": 59}}, "120": {"start": {"line": 121, "column": 0}, "end": {"line": 121, "column": 3}}, "125": {"start": {"line": 126, "column": 0}, "end": {"line": 126, "column": 61}}, "126": {"start": {"line": 127, "column": 0}, "end": {"line": 127, "column": 62}}, "127": {"start": {"line": 128, "column": 0}, "end": {"line": 128, "column": 17}}, "128": {"start": {"line": 129, "column": 0}, "end": {"line": 129, "column": 40}}, "129": {"start": {"line": 130, "column": 0}, "end": {"line": 130, "column": 5}}, "131": {"start": {"line": 132, "column": 0}, "end": {"line": 132, "column": 39}}, "132": {"start": {"line": 133, "column": 0}, "end": {"line": 133, "column": 54}}, "133": {"start": {"line": 134, "column": 0}, "end": {"line": 134, "column": 5}}, "135": {"start": {"line": 136, "column": 0}, "end": {"line": 136, "column": 64}}, "136": {"start": {"line": 137, "column": 0}, "end": {"line": 137, "column": 3}}, "141": {"start": {"line": 142, "column": 0}, "end": {"line": 142, "column": 64}}, "142": {"start": {"line": 143, "column": 0}, "end": {"line": 143, "column": 75}}, "143": {"start": {"line": 144, "column": 0}, "end": {"line": 144, "column": 3}}, "148": {"start": {"line": 149, "column": 0}, "end": {"line": 149, "column": 52}}, "153": {"start": {"line": 154, "column": 0}, "end": {"line": 154, "column": 6}}, "154": {"start": {"line": 155, "column": 0}, "end": {"line": 155, "column": 66}}, "156": {"start": {"line": 157, "column": 0}, "end": {"line": 157, "column": 37}}, "157": {"start": {"line": 158, "column": 0}, "end": {"line": 158, "column": 87}}, "158": {"start": {"line": 159, "column": 0}, "end": {"line": 159, "column": 30}}, "159": {"start": {"line": 160, "column": 0}, "end": {"line": 160, "column": 52}}, "160": {"start": {"line": 161, "column": 0}, "end": {"line": 161, "column": 64}}, "161": {"start": {"line": 162, "column": 0}, "end": {"line": 162, "column": 32}}, "162": {"start": {"line": 163, "column": 0}, "end": {"line": 163, "column": 52}}, "163": {"start": {"line": 164, "column": 0}, "end": {"line": 164, "column": 66}}, "165": {"start": {"line": 166, "column": 0}, "end": {"line": 166, "column": 12}}, "166": {"start": {"line": 167, "column": 0}, "end": {"line": 167, "column": 18}}, "167": {"start": {"line": 168, "column": 0}, "end": {"line": 168, "column": 22}}, "168": {"start": {"line": 169, "column": 0}, "end": {"line": 169, "column": 18}}, "169": {"start": {"line": 170, "column": 0}, "end": {"line": 170, "column": 20}}, "170": {"start": {"line": 171, "column": 0}, "end": {"line": 171, "column": 5}}, "171": {"start": {"line": 172, "column": 0}, "end": {"line": 172, "column": 3}}, "176": {"start": {"line": 177, "column": 0}, "end": {"line": 177, "column": 66}}, "181": {"start": {"line": 182, "column": 0}, "end": {"line": 182, "column": 6}}, "182": {"start": {"line": 183, "column": 0}, "end": {"line": 183, "column": 80}}, "184": {"start": {"line": 185, "column": 0}, "end": {"line": 185, "column": 37}}, "185": {"start": {"line": 186, "column": 0}, "end": {"line": 186, "column": 87}}, "186": {"start": {"line": 187, "column": 0}, "end": {"line": 187, "column": 30}}, "187": {"start": {"line": 188, "column": 0}, "end": {"line": 188, "column": 52}}, "188": {"start": {"line": 189, "column": 0}, "end": {"line": 189, "column": 64}}, "189": {"start": {"line": 190, "column": 0}, "end": {"line": 190, "column": 32}}, "190": {"start": {"line": 191, "column": 0}, "end": {"line": 191, "column": 52}}, "191": {"start": {"line": 192, "column": 0}, "end": {"line": 192, "column": 66}}, "193": {"start": {"line": 194, "column": 0}, "end": {"line": 194, "column": 12}}, "194": {"start": {"line": 195, "column": 0}, "end": {"line": 195, "column": 18}}, "195": {"start": {"line": 196, "column": 0}, "end": {"line": 196, "column": 22}}, "196": {"start": {"line": 197, "column": 0}, "end": {"line": 197, "column": 18}}, "197": {"start": {"line": 198, "column": 0}, "end": {"line": 198, "column": 20}}, "198": {"start": {"line": 199, "column": 0}, "end": {"line": 199, "column": 5}}, "199": {"start": {"line": 200, "column": 0}, "end": {"line": 200, "column": 3}}, "204": {"start": {"line": 205, "column": 0}, "end": {"line": 205, "column": 56}}, "205": {"start": {"line": 206, "column": 0}, "end": {"line": 206, "column": 47}}, "206": {"start": {"line": 207, "column": 0}, "end": {"line": 207, "column": 3}}, "211": {"start": {"line": 212, "column": 0}, "end": {"line": 212, "column": 58}}, "212": {"start": {"line": 213, "column": 0}, "end": {"line": 213, "column": 56}}, "213": {"start": {"line": 214, "column": 0}, "end": {"line": 214, "column": 3}}, "214": {"start": {"line": 215, "column": 0}, "end": {"line": 215, "column": 1}}}, "s": {"25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "35": 0, "36": 0, "38": 0, "39": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "53": 0, "56": 0, "58": 0, "59": 0, "64": 0, "65": 0, "67": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "77": 0, "78": 0, "81": 0, "82": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "90": 0, "95": 0, "96": 0, "97": 0, "102": 0, "103": 0, "104": 0, "109": 0, "110": 0, "111": 0, "112": 0, "113": 0, "114": 0, "115": 0, "116": 0, "117": 0, "119": 0, "120": 0, "125": 0, "126": 0, "127": 0, "128": 0, "129": 0, "131": 0, "132": 0, "133": 0, "135": 0, "136": 0, "141": 0, "142": 0, "143": 0, "148": 0, "153": 0, "154": 0, "156": 0, "157": 0, "158": 0, "159": 0, "160": 0, "161": 0, "162": 0, "163": 0, "165": 0, "166": 0, "167": 0, "168": 0, "169": 0, "170": 0, "171": 0, "176": 0, "181": 0, "182": 0, "184": 0, "185": 0, "186": 0, "187": 0, "188": 0, "189": 0, "190": 0, "191": 0, "193": 0, "194": 0, "195": 0, "196": 0, "197": 0, "198": 0, "199": 0, "204": 0, "205": 0, "206": 0, "211": 0, "212": 0, "213": 0, "214": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 5411}, "end": {"line": 215, "column": 1}}, "locations": [{"start": {"line": 1, "column": 5411}, "end": {"line": 215, "column": 1}}]}}, "b": {"0": [1]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 5411}, "end": {"line": 215, "column": 1}}, "loc": {"start": {"line": 1, "column": 5411}, "end": {"line": 215, "column": 1}}, "line": 1}}, "f": {"0": 1}}, "/Users/<USER>/code/open-trpc/packages/server-core/src/core/services/session-token.service.ts": {"path": "/Users/<USER>/code/open-trpc/packages/server-core/src/core/services/session-token.service.ts", "all": true, "statementMap": {"9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 34}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 51}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 56}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 14}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 25}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 40}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 6}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 86}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 66}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 67}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 41}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 13}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 20}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 28}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 5}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 25}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 16}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 33}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 12}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 40}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 5}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 23}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 3}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 84}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 67}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 50}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 19}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 17}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 5}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 9}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 52}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 21}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 17}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 5}}, "57": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 3}}, "62": {"start": {"line": 63, "column": 0}, "end": {"line": 63, "column": 69}}, "63": {"start": {"line": 64, "column": 0}, "end": {"line": 64, "column": 67}}, "64": {"start": {"line": 65, "column": 0}, "end": {"line": 65, "column": 52}}, "66": {"start": {"line": 67, "column": 0}, "end": {"line": 67, "column": 18}}, "67": {"start": {"line": 68, "column": 0}, "end": {"line": 68, "column": 18}}, "68": {"start": {"line": 69, "column": 0}, "end": {"line": 69, "column": 5}}, "70": {"start": {"line": 71, "column": 0}, "end": {"line": 71, "column": 73}}, "71": {"start": {"line": 72, "column": 0}, "end": {"line": 72, "column": 15}}, "72": {"start": {"line": 73, "column": 0}, "end": {"line": 73, "column": 3}}, "77": {"start": {"line": 78, "column": 0}, "end": {"line": 78, "column": 68}}, "78": {"start": {"line": 79, "column": 0}, "end": {"line": 79, "column": 67}}, "79": {"start": {"line": 80, "column": 0}, "end": {"line": 80, "column": 49}}, "80": {"start": {"line": 81, "column": 0}, "end": {"line": 81, "column": 21}}, "81": {"start": {"line": 82, "column": 0}, "end": {"line": 82, "column": 3}}, "86": {"start": {"line": 87, "column": 0}, "end": {"line": 87, "column": 67}}, "87": {"start": {"line": 88, "column": 0}, "end": {"line": 88, "column": 67}}, "88": {"start": {"line": 89, "column": 0}, "end": {"line": 89, "column": 41}}, "89": {"start": {"line": 90, "column": 0}, "end": {"line": 90, "column": 3}}, "94": {"start": {"line": 95, "column": 0}, "end": {"line": 95, "column": 61}}, "95": {"start": {"line": 96, "column": 0}, "end": {"line": 96, "column": 52}}, "96": {"start": {"line": 97, "column": 0}, "end": {"line": 97, "column": 47}}, "98": {"start": {"line": 99, "column": 0}, "end": {"line": 99, "column": 24}}, "100": {"start": {"line": 101, "column": 0}, "end": {"line": 101, "column": 29}}, "101": {"start": {"line": 102, "column": 0}, "end": {"line": 102, "column": 47}}, "102": {"start": {"line": 103, "column": 0}, "end": {"line": 103, "column": 20}}, "103": {"start": {"line": 104, "column": 0}, "end": {"line": 104, "column": 13}}, "104": {"start": {"line": 105, "column": 0}, "end": {"line": 105, "column": 62}}, "105": {"start": {"line": 106, "column": 0}, "end": {"line": 106, "column": 39}}, "106": {"start": {"line": 107, "column": 0}, "end": {"line": 107, "column": 37}}, "107": {"start": {"line": 108, "column": 0}, "end": {"line": 108, "column": 26}}, "108": {"start": {"line": 109, "column": 0}, "end": {"line": 109, "column": 11}}, "109": {"start": {"line": 110, "column": 0}, "end": {"line": 110, "column": 25}}, "111": {"start": {"line": 112, "column": 0}, "end": {"line": 112, "column": 9}}, "112": {"start": {"line": 113, "column": 0}, "end": {"line": 113, "column": 7}}, "113": {"start": {"line": 114, "column": 0}, "end": {"line": 114, "column": 5}}, "115": {"start": {"line": 116, "column": 0}, "end": {"line": 116, "column": 23}}, "116": {"start": {"line": 117, "column": 0}, "end": {"line": 117, "column": 3}}, "121": {"start": {"line": 122, "column": 0}, "end": {"line": 122, "column": 75}}, "122": {"start": {"line": 123, "column": 0}, "end": {"line": 123, "column": 52}}, "123": {"start": {"line": 124, "column": 0}, "end": {"line": 124, "column": 47}}, "125": {"start": {"line": 126, "column": 0}, "end": {"line": 126, "column": 24}}, "127": {"start": {"line": 128, "column": 0}, "end": {"line": 128, "column": 29}}, "128": {"start": {"line": 129, "column": 0}, "end": {"line": 129, "column": 47}}, "129": {"start": {"line": 130, "column": 0}, "end": {"line": 130, "column": 20}}, "130": {"start": {"line": 131, "column": 0}, "end": {"line": 131, "column": 13}}, "131": {"start": {"line": 132, "column": 0}, "end": {"line": 132, "column": 62}}, "132": {"start": {"line": 133, "column": 0}, "end": {"line": 133, "column": 53}}, "133": {"start": {"line": 134, "column": 0}, "end": {"line": 134, "column": 37}}, "134": {"start": {"line": 135, "column": 0}, "end": {"line": 135, "column": 26}}, "135": {"start": {"line": 136, "column": 0}, "end": {"line": 136, "column": 11}}, "136": {"start": {"line": 137, "column": 0}, "end": {"line": 137, "column": 25}}, "138": {"start": {"line": 139, "column": 0}, "end": {"line": 139, "column": 9}}, "139": {"start": {"line": 140, "column": 0}, "end": {"line": 140, "column": 7}}, "140": {"start": {"line": 141, "column": 0}, "end": {"line": 141, "column": 5}}, "142": {"start": {"line": 143, "column": 0}, "end": {"line": 143, "column": 23}}, "143": {"start": {"line": 144, "column": 0}, "end": {"line": 144, "column": 3}}, "148": {"start": {"line": 149, "column": 0}, "end": {"line": 149, "column": 68}}, "149": {"start": {"line": 150, "column": 0}, "end": {"line": 150, "column": 52}}, "150": {"start": {"line": 151, "column": 0}, "end": {"line": 151, "column": 47}}, "152": {"start": {"line": 153, "column": 0}, "end": {"line": 153, "column": 17}}, "154": {"start": {"line": 155, "column": 0}, "end": {"line": 155, "column": 29}}, "155": {"start": {"line": 156, "column": 0}, "end": {"line": 156, "column": 47}}, "156": {"start": {"line": 157, "column": 0}, "end": {"line": 157, "column": 20}}, "157": {"start": {"line": 158, "column": 0}, "end": {"line": 158, "column": 13}}, "158": {"start": {"line": 159, "column": 0}, "end": {"line": 159, "column": 62}}, "159": {"start": {"line": 160, "column": 0}, "end": {"line": 160, "column": 39}}, "160": {"start": {"line": 161, "column": 0}, "end": {"line": 161, "column": 19}}, "161": {"start": {"line": 162, "column": 0}, "end": {"line": 162, "column": 11}}, "162": {"start": {"line": 163, "column": 0}, "end": {"line": 163, "column": 25}}, "164": {"start": {"line": 165, "column": 0}, "end": {"line": 165, "column": 9}}, "165": {"start": {"line": 166, "column": 0}, "end": {"line": 166, "column": 7}}, "166": {"start": {"line": 167, "column": 0}, "end": {"line": 167, "column": 5}}, "168": {"start": {"line": 169, "column": 0}, "end": {"line": 169, "column": 16}}, "169": {"start": {"line": 170, "column": 0}, "end": {"line": 170, "column": 3}}, "170": {"start": {"line": 171, "column": 0}, "end": {"line": 171, "column": 1}}}, "s": {"9": 0, "10": 0, "11": 0, "13": 0, "14": 0, "15": 0, "16": 0, "21": 0, "22": 0, "23": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "38": 0, "39": 0, "44": 0, "45": 0, "46": 0, "48": 0, "49": 0, "50": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "62": 0, "63": 0, "64": 0, "66": 0, "67": 0, "68": 0, "70": 0, "71": 0, "72": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "86": 0, "87": 0, "88": 0, "89": 0, "94": 0, "95": 0, "96": 0, "98": 0, "100": 0, "101": 0, "102": 0, "103": 0, "104": 0, "105": 0, "106": 0, "107": 0, "108": 0, "109": 0, "111": 0, "112": 0, "113": 0, "115": 0, "116": 0, "121": 0, "122": 0, "123": 0, "125": 0, "127": 0, "128": 0, "129": 0, "130": 0, "131": 0, "132": 0, "133": 0, "134": 0, "135": 0, "136": 0, "138": 0, "139": 0, "140": 0, "142": 0, "143": 0, "148": 0, "149": 0, "150": 0, "152": 0, "154": 0, "155": 0, "156": 0, "157": 0, "158": 0, "159": 0, "160": 0, "161": 0, "162": 0, "164": 0, "165": 0, "166": 0, "168": 0, "169": 0, "170": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 4095}, "end": {"line": 171, "column": 1}}, "locations": [{"start": {"line": 1, "column": 4095}, "end": {"line": 171, "column": 1}}]}}, "b": {"0": [1]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 4095}, "end": {"line": 171, "column": 1}}, "loc": {"start": {"line": 1, "column": 4095}, "end": {"line": 171, "column": 1}}, "line": 1}}, "f": {"0": 1}}, "/Users/<USER>/code/open-trpc/packages/server-core/src/core/services/user.service.ts": {"path": "/Users/<USER>/code/open-trpc/packages/server-core/src/core/services/user.service.ts", "all": false, "statementMap": {"4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 26}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 14}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 43}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 36}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 6}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 59}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 55}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 16}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 17}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 5}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 45}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 19}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 3}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 65}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 61}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 16}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 17}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 5}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 45}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 19}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 3}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 65}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 61}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 16}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 17}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 5}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 45}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 19}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 3}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 61}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 28}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 80}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 5}}, "58": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 59}}, "59": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 45}}, "60": {"start": {"line": 61, "column": 0}, "end": {"line": 61, "column": 19}}, "61": {"start": {"line": 62, "column": 0}, "end": {"line": 62, "column": 3}}, "66": {"start": {"line": 67, "column": 0}, "end": {"line": 67, "column": 88}}, "68": {"start": {"line": 69, "column": 0}, "end": {"line": 69, "column": 27}}, "69": {"start": {"line": 70, "column": 0}, "end": {"line": 70, "column": 78}}, "70": {"start": {"line": 71, "column": 0}, "end": {"line": 71, "column": 5}}, "72": {"start": {"line": 73, "column": 0}, "end": {"line": 73, "column": 62}}, "73": {"start": {"line": 74, "column": 0}, "end": {"line": 74, "column": 16}}, "74": {"start": {"line": 75, "column": 0}, "end": {"line": 75, "column": 17}}, "75": {"start": {"line": 76, "column": 0}, "end": {"line": 76, "column": 5}}, "77": {"start": {"line": 78, "column": 0}, "end": {"line": 78, "column": 45}}, "78": {"start": {"line": 79, "column": 0}, "end": {"line": 79, "column": 19}}, "79": {"start": {"line": 80, "column": 0}, "end": {"line": 80, "column": 3}}, "84": {"start": {"line": 85, "column": 0}, "end": {"line": 85, "column": 96}}, "85": {"start": {"line": 86, "column": 0}, "end": {"line": 86, "column": 55}}, "86": {"start": {"line": 87, "column": 0}, "end": {"line": 87, "column": 16}}, "87": {"start": {"line": 88, "column": 0}, "end": {"line": 88, "column": 39}}, "88": {"start": {"line": 89, "column": 0}, "end": {"line": 89, "column": 5}}, "91": {"start": {"line": 92, "column": 0}, "end": {"line": 92, "column": 24}}, "92": {"start": {"line": 93, "column": 0}, "end": {"line": 93, "column": 98}}, "93": {"start": {"line": 94, "column": 0}, "end": {"line": 94, "column": 32}}, "94": {"start": {"line": 95, "column": 0}, "end": {"line": 95, "column": 47}}, "95": {"start": {"line": 96, "column": 0}, "end": {"line": 96, "column": 7}}, "96": {"start": {"line": 97, "column": 0}, "end": {"line": 97, "column": 5}}, "99": {"start": {"line": 100, "column": 0}, "end": {"line": 100, "column": 78}}, "101": {"start": {"line": 102, "column": 0}, "end": {"line": 102, "column": 93}}, "102": {"start": {"line": 103, "column": 0}, "end": {"line": 103, "column": 24}}, "103": {"start": {"line": 104, "column": 0}, "end": {"line": 104, "column": 3}}, "108": {"start": {"line": 109, "column": 0}, "end": {"line": 109, "column": 69}}, "109": {"start": {"line": 110, "column": 0}, "end": {"line": 110, "column": 72}}, "110": {"start": {"line": 111, "column": 0}, "end": {"line": 111, "column": 90}}, "111": {"start": {"line": 112, "column": 0}, "end": {"line": 112, "column": 24}}, "112": {"start": {"line": 113, "column": 0}, "end": {"line": 113, "column": 3}}, "117": {"start": {"line": 118, "column": 0}, "end": {"line": 118, "column": 51}}, "118": {"start": {"line": 119, "column": 0}, "end": {"line": 119, "column": 85}}, "119": {"start": {"line": 120, "column": 0}, "end": {"line": 120, "column": 24}}, "120": {"start": {"line": 121, "column": 0}, "end": {"line": 121, "column": 3}}, "125": {"start": {"line": 126, "column": 0}, "end": {"line": 126, "column": 51}}, "126": {"start": {"line": 127, "column": 0}, "end": {"line": 127, "column": 85}}, "127": {"start": {"line": 128, "column": 0}, "end": {"line": 128, "column": 24}}, "128": {"start": {"line": 129, "column": 0}, "end": {"line": 129, "column": 3}}, "133": {"start": {"line": 134, "column": 0}, "end": {"line": 134, "column": 79}}, "134": {"start": {"line": 135, "column": 0}, "end": {"line": 135, "column": 76}}, "135": {"start": {"line": 136, "column": 0}, "end": {"line": 136, "column": 16}}, "136": {"start": {"line": 137, "column": 0}, "end": {"line": 137, "column": 17}}, "137": {"start": {"line": 138, "column": 0}, "end": {"line": 138, "column": 5}}, "139": {"start": {"line": 140, "column": 0}, "end": {"line": 140, "column": 45}}, "140": {"start": {"line": 141, "column": 0}, "end": {"line": 141, "column": 19}}, "141": {"start": {"line": 142, "column": 0}, "end": {"line": 142, "column": 3}}, "146": {"start": {"line": 147, "column": 0}, "end": {"line": 147, "column": 22}}, "147": {"start": {"line": 148, "column": 0}, "end": {"line": 148, "column": 15}}, "148": {"start": {"line": 149, "column": 0}, "end": {"line": 149, "column": 57}}, "149": {"start": {"line": 150, "column": 0}, "end": {"line": 150, "column": 31}}, "151": {"start": {"line": 152, "column": 0}, "end": {"line": 152, "column": 24}}, "152": {"start": {"line": 153, "column": 0}, "end": {"line": 153, "column": 79}}, "153": {"start": {"line": 154, "column": 0}, "end": {"line": 154, "column": 51}}, "154": {"start": {"line": 155, "column": 0}, "end": {"line": 155, "column": 47}}, "155": {"start": {"line": 156, "column": 0}, "end": {"line": 156, "column": 7}}, "156": {"start": {"line": 157, "column": 0}, "end": {"line": 157, "column": 5}}, "159": {"start": {"line": 160, "column": 0}, "end": {"line": 160, "column": 24}}, "160": {"start": {"line": 161, "column": 0}, "end": {"line": 161, "column": 79}}, "161": {"start": {"line": 162, "column": 0}, "end": {"line": 162, "column": 51}}, "162": {"start": {"line": 163, "column": 0}, "end": {"line": 163, "column": 47}}, "163": {"start": {"line": 164, "column": 0}, "end": {"line": 164, "column": 7}}, "164": {"start": {"line": 165, "column": 0}, "end": {"line": 165, "column": 5}}, "166": {"start": {"line": 167, "column": 0}, "end": {"line": 167, "column": 62}}, "167": {"start": {"line": 168, "column": 0}, "end": {"line": 168, "column": 16}}, "168": {"start": {"line": 169, "column": 0}, "end": {"line": 169, "column": 17}}, "169": {"start": {"line": 170, "column": 0}, "end": {"line": 170, "column": 5}}, "171": {"start": {"line": 172, "column": 0}, "end": {"line": 172, "column": 45}}, "172": {"start": {"line": 173, "column": 0}, "end": {"line": 173, "column": 19}}, "173": {"start": {"line": 174, "column": 0}, "end": {"line": 174, "column": 3}}, "178": {"start": {"line": 179, "column": 0}, "end": {"line": 179, "column": 50}}, "179": {"start": {"line": 180, "column": 0}, "end": {"line": 180, "column": 41}}, "180": {"start": {"line": 181, "column": 0}, "end": {"line": 181, "column": 3}}, "185": {"start": {"line": 186, "column": 0}, "end": {"line": 186, "column": 50}}, "186": {"start": {"line": 187, "column": 0}, "end": {"line": 187, "column": 41}}, "187": {"start": {"line": 188, "column": 0}, "end": {"line": 188, "column": 3}}, "192": {"start": {"line": 193, "column": 0}, "end": {"line": 193, "column": 54}}, "193": {"start": {"line": 194, "column": 0}, "end": {"line": 194, "column": 61}}, "194": {"start": {"line": 195, "column": 0}, "end": {"line": 195, "column": 17}}, "195": {"start": {"line": 196, "column": 0}, "end": {"line": 196, "column": 3}}, "200": {"start": {"line": 201, "column": 0}, "end": {"line": 201, "column": 54}}, "201": {"start": {"line": 202, "column": 0}, "end": {"line": 202, "column": 61}}, "202": {"start": {"line": 203, "column": 0}, "end": {"line": 203, "column": 17}}, "203": {"start": {"line": 204, "column": 0}, "end": {"line": 204, "column": 3}}, "204": {"start": {"line": 205, "column": 0}, "end": {"line": 205, "column": 1}}}, "s": {"4": 1, "5": 1, "6": 12, "7": 12, "8": 12, "13": 1, "14": 2, "15": 2, "16": 1, "17": 1, "19": 1, "20": 1, "21": 2, "26": 1, "27": 1, "28": 1, "29": 0, "30": 0, "32": 1, "33": 1, "34": 1, "39": 1, "40": 0, "41": 0, "42": 0, "43": 0, "45": 0, "46": 0, "47": 0, "52": 1, "54": 2, "55": 1, "56": 1, "58": 2, "59": 2, "60": 2, "61": 2, "66": 1, "68": 0, "69": 0, "70": 0, "72": 0, "73": 0, "74": 0, "75": 0, "77": 0, "78": 0, "79": 0, "84": 1, "85": 4, "86": 4, "87": 1, "88": 1, "91": 4, "92": 2, "93": 2, "94": 1, "95": 1, "96": 2, "99": 2, "101": 2, "102": 2, "103": 4, "108": 1, "109": 0, "110": 0, "111": 0, "112": 0, "117": 1, "118": 0, "119": 0, "120": 0, "125": 1, "126": 0, "127": 0, "128": 0, "133": 1, "134": 0, "135": 0, "136": 0, "137": 0, "139": 0, "140": 0, "141": 0, "146": 1, "147": 3, "148": 3, "149": 3, "151": 3, "152": 3, "153": 3, "154": 1, "155": 1, "156": 3, "159": 3, "160": 0, "161": 0, "162": 0, "163": 0, "164": 0, "166": 2, "167": 3, "168": 0, "169": 0, "171": 2, "172": 2, "173": 3, "178": 1, "179": 0, "180": 0, "185": 1, "186": 0, "187": 0, "192": 1, "193": 0, "194": 0, "195": 0, "200": 1, "201": 0, "202": 0, "203": 0, "204": 1}, "branchMap": {"0": {"type": "branch", "line": 6, "loc": {"start": {"line": 6, "column": 2}, "end": {"line": 9, "column": 6}}, "locations": [{"start": {"line": 6, "column": 2}, "end": {"line": 9, "column": 6}}]}, "1": {"type": "branch", "line": 14, "loc": {"start": {"line": 14, "column": 2}, "end": {"line": 22, "column": 3}}, "locations": [{"start": {"line": 14, "column": 2}, "end": {"line": 22, "column": 3}}]}, "2": {"type": "branch", "line": 16, "loc": {"start": {"line": 16, "column": 15}, "end": {"line": 21, "column": 19}}, "locations": [{"start": {"line": 16, "column": 15}, "end": {"line": 21, "column": 19}}]}, "3": {"type": "branch", "line": 27, "loc": {"start": {"line": 27, "column": 2}, "end": {"line": 35, "column": 3}}, "locations": [{"start": {"line": 27, "column": 2}, "end": {"line": 35, "column": 3}}]}, "4": {"type": "branch", "line": 29, "loc": {"start": {"line": 29, "column": 15}, "end": {"line": 31, "column": 5}}, "locations": [{"start": {"line": 29, "column": 15}, "end": {"line": 31, "column": 5}}]}, "5": {"type": "branch", "line": 53, "loc": {"start": {"line": 53, "column": 2}, "end": {"line": 62, "column": 3}}, "locations": [{"start": {"line": 53, "column": 2}, "end": {"line": 62, "column": 3}}]}, "6": {"type": "branch", "line": 55, "loc": {"start": {"line": 55, "column": 27}, "end": {"line": 57, "column": 5}}, "locations": [{"start": {"line": 55, "column": 27}, "end": {"line": 57, "column": 5}}]}, "7": {"type": "branch", "line": 85, "loc": {"start": {"line": 85, "column": 2}, "end": {"line": 104, "column": 3}}, "locations": [{"start": {"line": 85, "column": 2}, "end": {"line": 104, "column": 3}}]}, "8": {"type": "branch", "line": 87, "loc": {"start": {"line": 87, "column": 15}, "end": {"line": 89, "column": 5}}, "locations": [{"start": {"line": 87, "column": 15}, "end": {"line": 89, "column": 5}}]}, "9": {"type": "branch", "line": 89, "loc": {"start": {"line": 89, "column": 4}, "end": {"line": 92, "column": 23}}, "locations": [{"start": {"line": 89, "column": 4}, "end": {"line": 92, "column": 23}}]}, "10": {"type": "branch", "line": 92, "loc": {"start": {"line": 92, "column": 23}, "end": {"line": 103, "column": 24}}, "locations": [{"start": {"line": 92, "column": 23}, "end": {"line": 103, "column": 24}}]}, "11": {"type": "branch", "line": 94, "loc": {"start": {"line": 94, "column": 31}, "end": {"line": 96, "column": 7}}, "locations": [{"start": {"line": 94, "column": 31}, "end": {"line": 96, "column": 7}}]}, "12": {"type": "branch", "line": 147, "loc": {"start": {"line": 147, "column": 2}, "end": {"line": 174, "column": 3}}, "locations": [{"start": {"line": 147, "column": 2}, "end": {"line": 174, "column": 3}}]}, "13": {"type": "branch", "line": 154, "loc": {"start": {"line": 154, "column": 10}, "end": {"line": 154, "column": 50}}, "locations": [{"start": {"line": 154, "column": 10}, "end": {"line": 154, "column": 50}}]}, "14": {"type": "branch", "line": 154, "loc": {"start": {"line": 154, "column": 50}, "end": {"line": 156, "column": 7}}, "locations": [{"start": {"line": 154, "column": 50}, "end": {"line": 156, "column": 7}}]}, "15": {"type": "branch", "line": 157, "loc": {"start": {"line": 157, "column": 4}, "end": {"line": 160, "column": 23}}, "locations": [{"start": {"line": 157, "column": 4}, "end": {"line": 160, "column": 23}}]}, "16": {"type": "branch", "line": 160, "loc": {"start": {"line": 160, "column": 23}, "end": {"line": 165, "column": 5}}, "locations": [{"start": {"line": 160, "column": 23}, "end": {"line": 165, "column": 5}}]}, "17": {"type": "branch", "line": 165, "loc": {"start": {"line": 165, "column": 4}, "end": {"line": 168, "column": 15}}, "locations": [{"start": {"line": 165, "column": 4}, "end": {"line": 168, "column": 15}}]}, "18": {"type": "branch", "line": 168, "loc": {"start": {"line": 168, "column": 15}, "end": {"line": 170, "column": 5}}, "locations": [{"start": {"line": 168, "column": 15}, "end": {"line": 170, "column": 5}}]}, "19": {"type": "branch", "line": 170, "loc": {"start": {"line": 170, "column": 4}, "end": {"line": 173, "column": 19}}, "locations": [{"start": {"line": 170, "column": 4}, "end": {"line": 173, "column": 19}}]}}, "b": {"0": [12], "1": [2], "2": [1], "3": [1], "4": [0], "5": [2], "6": [1], "7": [4], "8": [1], "9": [3], "10": [2], "11": [1], "12": [3], "13": [2], "14": [1], "15": [2], "16": [0], "17": [2], "18": [0], "19": [2]}, "fnMap": {"0": {"name": "UserService", "decl": {"start": {"line": 6, "column": 2}, "end": {"line": 9, "column": 6}}, "loc": {"start": {"line": 6, "column": 2}, "end": {"line": 9, "column": 6}}, "line": 6}, "1": {"name": "getUserById", "decl": {"start": {"line": 14, "column": 2}, "end": {"line": 22, "column": 3}}, "loc": {"start": {"line": 14, "column": 2}, "end": {"line": 22, "column": 3}}, "line": 14}, "2": {"name": "getUserByEmail", "decl": {"start": {"line": 27, "column": 2}, "end": {"line": 35, "column": 3}}, "loc": {"start": {"line": 27, "column": 2}, "end": {"line": 35, "column": 3}}, "line": 27}, "3": {"name": "getUserByPhone", "decl": {"start": {"line": 40, "column": 2}, "end": {"line": 48, "column": 3}}, "loc": {"start": {"line": 40, "column": 2}, "end": {"line": 48, "column": 3}}, "line": 40}, "4": {"name": "createUser", "decl": {"start": {"line": 53, "column": 2}, "end": {"line": 62, "column": 3}}, "loc": {"start": {"line": 53, "column": 2}, "end": {"line": 62, "column": 3}}, "line": 53}, "5": {"name": "updateUser", "decl": {"start": {"line": 67, "column": 2}, "end": {"line": 80, "column": 3}}, "loc": {"start": {"line": 67, "column": 2}, "end": {"line": 80, "column": 3}}, "line": 67}, "6": {"name": "updatePassword", "decl": {"start": {"line": 85, "column": 2}, "end": {"line": 104, "column": 3}}, "loc": {"start": {"line": 85, "column": 2}, "end": {"line": 104, "column": 3}}, "line": 85}, "7": {"name": "setPassword", "decl": {"start": {"line": 109, "column": 2}, "end": {"line": 113, "column": 3}}, "loc": {"start": {"line": 109, "column": 2}, "end": {"line": 113, "column": 3}}, "line": 109}, "8": {"name": "verifyEmail", "decl": {"start": {"line": 118, "column": 2}, "end": {"line": 121, "column": 3}}, "loc": {"start": {"line": 118, "column": 2}, "end": {"line": 121, "column": 3}}, "line": 118}, "9": {"name": "verifyPhone", "decl": {"start": {"line": 126, "column": 2}, "end": {"line": 129, "column": 3}}, "loc": {"start": {"line": 126, "column": 2}, "end": {"line": 129, "column": 3}}, "line": 126}, "10": {"name": "updateAvatar", "decl": {"start": {"line": 134, "column": 2}, "end": {"line": 142, "column": 3}}, "loc": {"start": {"line": 134, "column": 2}, "end": {"line": 142, "column": 3}}, "line": 134}, "11": {"name": "updateProfile", "decl": {"start": {"line": 147, "column": 2}, "end": {"line": 174, "column": 3}}, "loc": {"start": {"line": 147, "column": 2}, "end": {"line": 174, "column": 3}}, "line": 147}, "12": {"name": "deleteUser", "decl": {"start": {"line": 179, "column": 2}, "end": {"line": 181, "column": 3}}, "loc": {"start": {"line": 179, "column": 2}, "end": {"line": 181, "column": 3}}, "line": 179}, "13": {"name": "userExists", "decl": {"start": {"line": 186, "column": 2}, "end": {"line": 188, "column": 3}}, "loc": {"start": {"line": 186, "column": 2}, "end": {"line": 188, "column": 3}}, "line": 186}, "14": {"name": "emailExists", "decl": {"start": {"line": 193, "column": 2}, "end": {"line": 196, "column": 3}}, "loc": {"start": {"line": 193, "column": 2}, "end": {"line": 196, "column": 3}}, "line": 193}, "15": {"name": "phoneExists", "decl": {"start": {"line": 201, "column": 2}, "end": {"line": 204, "column": 3}}, "loc": {"start": {"line": 201, "column": 2}, "end": {"line": 204, "column": 3}}, "line": 201}}, "f": {"0": 12, "1": 2, "2": 1, "3": 0, "4": 2, "5": 0, "6": 4, "7": 0, "8": 0, "9": 0, "10": 0, "11": 3, "12": 0, "13": 0, "14": 0, "15": 0}}, "/Users/<USER>/code/open-trpc/packages/server-core/src/core/services/verification.service.ts": {"path": "/Users/<USER>/code/open-trpc/packages/server-core/src/core/services/verification.service.ts", "all": true, "statementMap": {"12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 34}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 50}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 55}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 52}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 38}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 21}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 24}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 26}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 22}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 65}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 68}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 36}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 11}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 18}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 27}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 5}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 89}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 15}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 3}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 19}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 23}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 27}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 21}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 53}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 68}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 45}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 19}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 54}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 5}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 56}}, "58": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 60}}, "59": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 48}}, "60": {"start": {"line": 61, "column": 0}, "end": {"line": 61, "column": 59}}, "61": {"start": {"line": 62, "column": 0}, "end": {"line": 62, "column": 5}}, "64": {"start": {"line": 65, "column": 0}, "end": {"line": 65, "column": 34}}, "65": {"start": {"line": 66, "column": 0}, "end": {"line": 66, "column": 24}}, "66": {"start": {"line": 67, "column": 0}, "end": {"line": 67, "column": 91}}, "67": {"start": {"line": 68, "column": 0}, "end": {"line": 68, "column": 15}}, "68": {"start": {"line": 69, "column": 0}, "end": {"line": 69, "column": 24}}, "69": {"start": {"line": 70, "column": 0}, "end": {"line": 70, "column": 82}}, "70": {"start": {"line": 71, "column": 0}, "end": {"line": 71, "column": 7}}, "71": {"start": {"line": 72, "column": 0}, "end": {"line": 72, "column": 5}}, "74": {"start": {"line": 75, "column": 0}, "end": {"line": 75, "column": 29}}, "75": {"start": {"line": 76, "column": 0}, "end": {"line": 76, "column": 45}}, "76": {"start": {"line": 77, "column": 0}, "end": {"line": 77, "column": 3}}, "81": {"start": {"line": 82, "column": 0}, "end": {"line": 82, "column": 22}}, "82": {"start": {"line": 83, "column": 0}, "end": {"line": 83, "column": 22}}, "83": {"start": {"line": 84, "column": 0}, "end": {"line": 84, "column": 60}}, "84": {"start": {"line": 85, "column": 0}, "end": {"line": 85, "column": 69}}, "85": {"start": {"line": 86, "column": 0}, "end": {"line": 86, "column": 50}}, "87": {"start": {"line": 88, "column": 0}, "end": {"line": 88, "column": 19}}, "88": {"start": {"line": 89, "column": 0}, "end": {"line": 89, "column": 53}}, "89": {"start": {"line": 90, "column": 0}, "end": {"line": 90, "column": 47}}, "91": {"start": {"line": 92, "column": 0}, "end": {"line": 92, "column": 25}}, "92": {"start": {"line": 93, "column": 0}, "end": {"line": 93, "column": 76}}, "93": {"start": {"line": 94, "column": 0}, "end": {"line": 94, "column": 7}}, "94": {"start": {"line": 95, "column": 0}, "end": {"line": 95, "column": 5}}, "97": {"start": {"line": 98, "column": 0}, "end": {"line": 98, "column": 62}}, "98": {"start": {"line": 99, "column": 0}, "end": {"line": 99, "column": 28}}, "99": {"start": {"line": 100, "column": 0}, "end": {"line": 100, "column": 3}}, "104": {"start": {"line": 105, "column": 0}, "end": {"line": 105, "column": 18}}, "105": {"start": {"line": 106, "column": 0}, "end": {"line": 106, "column": 23}}, "106": {"start": {"line": 107, "column": 0}, "end": {"line": 107, "column": 26}}, "107": {"start": {"line": 108, "column": 0}, "end": {"line": 108, "column": 20}}, "108": {"start": {"line": 109, "column": 0}, "end": {"line": 109, "column": 68}}, "109": {"start": {"line": 110, "column": 0}, "end": {"line": 110, "column": 29}}, "110": {"start": {"line": 111, "column": 0}, "end": {"line": 111, "column": 3}}, "115": {"start": {"line": 116, "column": 0}, "end": {"line": 116, "column": 19}}, "116": {"start": {"line": 117, "column": 0}, "end": {"line": 117, "column": 23}}, "117": {"start": {"line": 118, "column": 0}, "end": {"line": 118, "column": 26}}, "118": {"start": {"line": 119, "column": 0}, "end": {"line": 119, "column": 22}}, "119": {"start": {"line": 120, "column": 0}, "end": {"line": 120, "column": 68}}, "120": {"start": {"line": 121, "column": 0}, "end": {"line": 121, "column": 36}}, "121": {"start": {"line": 122, "column": 0}, "end": {"line": 122, "column": 3}}, "122": {"start": {"line": 123, "column": 0}, "end": {"line": 123, "column": 1}}}, "s": {"12": 0, "13": 0, "14": 0, "15": 0, "17": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "36": 0, "37": 0, "38": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "51": 0, "52": 0, "53": 0, "55": 0, "58": 0, "59": 0, "60": 0, "61": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "74": 0, "75": 0, "76": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "87": 0, "88": 0, "89": 0, "91": 0, "92": 0, "93": 0, "94": 0, "97": 0, "98": 0, "99": 0, "104": 0, "105": 0, "106": 0, "107": 0, "108": 0, "109": 0, "110": 0, "115": 0, "116": 0, "117": 0, "118": 0, "119": 0, "120": 0, "121": 0, "122": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 3072}, "end": {"line": 123, "column": 1}}, "locations": [{"start": {"line": 1, "column": 3072}, "end": {"line": 123, "column": 1}}]}}, "b": {"0": [1]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 3072}, "end": {"line": 123, "column": 1}}, "loc": {"start": {"line": 1, "column": 3072}, "end": {"line": 123, "column": 1}}, "line": 1}}, "f": {"0": 1}}, "/Users/<USER>/code/open-trpc/packages/server-core/src/db/client.ts": {"path": "/Users/<USER>/code/open-trpc/packages/server-core/src/db/client.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 37}}}, "s": {"0": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 7, "column": -40}}, "locations": [{"start": {"line": 1, "column": 0}, "end": {"line": 7, "column": -40}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 0}, "end": {"line": 7, "column": -40}}, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 7, "column": -40}}, "line": 1}}, "f": {"0": 0}}, "/Users/<USER>/code/open-trpc/packages/server-core/src/db/repositories/application.repository.ts": {"path": "/Users/<USER>/code/open-trpc/packages/server-core/src/db/repositories/application.repository.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 37}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 36}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 44}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 59}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 63}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 37}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 6}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 25}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 3}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 65}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 63}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 43}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 6}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 25}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 3}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 62}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 62}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 45}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 6}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 17}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 3}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 90}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 63}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 17}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 40}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 37}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 8}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 6}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 25}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 3}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 74}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 34}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 27}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 30}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 18}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 18}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 53}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 5}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 17}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 3}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 102}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 34}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 27}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 27}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 37}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 18}}, "58": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 25}}, "59": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 3}}, "61": {"start": {"line": 62, "column": 0}, "end": {"line": 62, "column": 46}}, "62": {"start": {"line": 63, "column": 0}, "end": {"line": 63, "column": 32}}, "63": {"start": {"line": 64, "column": 0}, "end": {"line": 64, "column": 27}}, "64": {"start": {"line": 65, "column": 0}, "end": {"line": 65, "column": 37}}, "66": {"start": {"line": 67, "column": 0}, "end": {"line": 67, "column": 37}}, "67": {"start": {"line": 68, "column": 0}, "end": {"line": 68, "column": 3}}, "69": {"start": {"line": 70, "column": 0}, "end": {"line": 70, "column": 46}}, "70": {"start": {"line": 71, "column": 0}, "end": {"line": 71, "column": 63}}, "71": {"start": {"line": 72, "column": 0}, "end": {"line": 72, "column": 37}}, "72": {"start": {"line": 73, "column": 0}, "end": {"line": 73, "column": 28}}, "73": {"start": {"line": 74, "column": 0}, "end": {"line": 74, "column": 6}}, "74": {"start": {"line": 75, "column": 0}, "end": {"line": 75, "column": 19}}, "75": {"start": {"line": 76, "column": 0}, "end": {"line": 76, "column": 3}}, "77": {"start": {"line": 78, "column": 0}, "end": {"line": 78, "column": 56}}, "78": {"start": {"line": 79, "column": 0}, "end": {"line": 79, "column": 63}}, "79": {"start": {"line": 80, "column": 0}, "end": {"line": 80, "column": 43}}, "80": {"start": {"line": 81, "column": 0}, "end": {"line": 81, "column": 28}}, "81": {"start": {"line": 82, "column": 0}, "end": {"line": 82, "column": 6}}, "82": {"start": {"line": 83, "column": 0}, "end": {"line": 83, "column": 19}}, "83": {"start": {"line": 84, "column": 0}, "end": {"line": 84, "column": 3}}, "84": {"start": {"line": 85, "column": 0}, "end": {"line": 85, "column": 1}}}, "s": {"0": 0, "4": 0, "5": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "44": 0, "45": 0, "46": 0, "48": 0, "49": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "58": 0, "59": 0, "61": 0, "62": 0, "63": 0, "64": 0, "66": 0, "67": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 85, "column": -105}}, "locations": [{"start": {"line": 1, "column": 0}, "end": {"line": 85, "column": -105}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 0}, "end": {"line": 85, "column": -105}}, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 85, "column": -105}}, "line": 1}}, "f": {"0": 0}}, "/Users/<USER>/code/open-trpc/packages/server-core/src/db/repositories/balance.repository.ts": {"path": "/Users/<USER>/code/open-trpc/packages/server-core/src/db/repositories/balance.repository.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 37}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 32}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 44}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 66}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 70}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 44}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 6}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 25}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 3}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 88}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 70}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 66}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 6}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 25}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 3}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 87}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 46}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 69}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 82}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 6}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 17}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 3}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 84}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 34}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 34}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 26}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 18}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 18}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 49}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 5}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 17}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 3}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 112}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 34}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 34}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 23}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 44}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 18}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 25}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 3}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 102}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 34}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 34}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 35}}, "57": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 66}}, "58": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 18}}, "60": {"start": {"line": 61, "column": 0}, "end": {"line": 61, "column": 25}}, "61": {"start": {"line": 62, "column": 0}, "end": {"line": 62, "column": 3}}, "63": {"start": {"line": 64, "column": 0}, "end": {"line": 64, "column": 46}}, "64": {"start": {"line": 65, "column": 0}, "end": {"line": 65, "column": 32}}, "65": {"start": {"line": 66, "column": 0}, "end": {"line": 66, "column": 34}}, "66": {"start": {"line": 67, "column": 0}, "end": {"line": 67, "column": 44}}, "68": {"start": {"line": 69, "column": 0}, "end": {"line": 69, "column": 37}}, "69": {"start": {"line": 70, "column": 0}, "end": {"line": 70, "column": 3}}, "71": {"start": {"line": 72, "column": 0}, "end": {"line": 72, "column": 57}}, "72": {"start": {"line": 73, "column": 0}, "end": {"line": 73, "column": 70}}, "73": {"start": {"line": 74, "column": 0}, "end": {"line": 74, "column": 66}}, "74": {"start": {"line": 75, "column": 0}, "end": {"line": 75, "column": 28}}, "75": {"start": {"line": 76, "column": 0}, "end": {"line": 76, "column": 6}}, "76": {"start": {"line": 77, "column": 0}, "end": {"line": 77, "column": 19}}, "77": {"start": {"line": 78, "column": 0}, "end": {"line": 78, "column": 3}}, "78": {"start": {"line": 79, "column": 0}, "end": {"line": 79, "column": 1}}}, "s": {"0": 0, "4": 0, "5": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "21": 0, "22": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "36": 0, "37": 0, "38": 0, "40": 0, "41": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "50": 0, "51": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "60": 0, "61": 0, "63": 0, "64": 0, "65": 0, "66": 0, "68": 0, "69": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 2474}, "end": {"line": 79, "column": 1}}, "locations": [{"start": {"line": 1, "column": 2474}, "end": {"line": 79, "column": 1}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 2474}, "end": {"line": 79, "column": 1}}, "loc": {"start": {"line": 1, "column": 2474}, "end": {"line": 79, "column": 1}}, "line": 1}}, "f": {"0": 0}}, "/Users/<USER>/code/open-trpc/packages/server-core/src/db/repositories/index.ts": {"path": "/Users/<USER>/code/open-trpc/packages/server-core/src/db/repositories/index.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 50}}}, "s": {"0": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 225}, "end": {"line": 4, "column": 52}}, "locations": [{"start": {"line": 1, "column": 225}, "end": {"line": 4, "column": 52}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 225}, "end": {"line": 4, "column": 52}}, "loc": {"start": {"line": 1, "column": 225}, "end": {"line": 4, "column": 52}}, "line": 1}}, "f": {"0": 0}}, "/Users/<USER>/code/open-trpc/packages/server-core/src/db/repositories/order.repository.ts": {"path": "/Users/<USER>/code/open-trpc/packages/server-core/src/db/repositories/order.repository.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 43}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 30}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 44}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 53}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 57}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 31}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 6}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 25}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 3}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 63}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 57}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 41}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 6}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 25}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 3}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 72}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 56}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 39}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 40}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 19}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 6}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 17}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 3}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 103}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 56}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 17}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 34}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 47}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 8}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 40}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 19}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 6}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 17}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 3}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 86}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 56}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 53}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 40}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 19}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 6}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 17}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 3}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 56}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 34}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 21}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 24}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 18}}, "57": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 18}}, "58": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 47}}, "59": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 5}}, "61": {"start": {"line": 62, "column": 0}, "end": {"line": 62, "column": 17}}, "62": {"start": {"line": 63, "column": 0}, "end": {"line": 63, "column": 3}}, "64": {"start": {"line": 65, "column": 0}, "end": {"line": 65, "column": 84}}, "65": {"start": {"line": 66, "column": 0}, "end": {"line": 66, "column": 34}}, "66": {"start": {"line": 67, "column": 0}, "end": {"line": 67, "column": 21}}, "67": {"start": {"line": 68, "column": 0}, "end": {"line": 68, "column": 21}}, "68": {"start": {"line": 69, "column": 0}, "end": {"line": 69, "column": 31}}, "69": {"start": {"line": 70, "column": 0}, "end": {"line": 70, "column": 18}}, "71": {"start": {"line": 72, "column": 0}, "end": {"line": 72, "column": 25}}, "72": {"start": {"line": 73, "column": 0}, "end": {"line": 73, "column": 3}}, "74": {"start": {"line": 75, "column": 0}, "end": {"line": 75, "column": 73}}, "75": {"start": {"line": 76, "column": 0}, "end": {"line": 76, "column": 34}}, "76": {"start": {"line": 77, "column": 0}, "end": {"line": 77, "column": 21}}, "77": {"start": {"line": 78, "column": 0}, "end": {"line": 78, "column": 22}}, "78": {"start": {"line": 79, "column": 0}, "end": {"line": 79, "column": 31}}, "79": {"start": {"line": 80, "column": 0}, "end": {"line": 80, "column": 18}}, "81": {"start": {"line": 82, "column": 0}, "end": {"line": 82, "column": 25}}, "82": {"start": {"line": 83, "column": 0}, "end": {"line": 83, "column": 3}}, "84": {"start": {"line": 85, "column": 0}, "end": {"line": 85, "column": 46}}, "85": {"start": {"line": 86, "column": 0}, "end": {"line": 86, "column": 32}}, "86": {"start": {"line": 87, "column": 0}, "end": {"line": 87, "column": 21}}, "87": {"start": {"line": 88, "column": 0}, "end": {"line": 88, "column": 31}}, "89": {"start": {"line": 90, "column": 0}, "end": {"line": 90, "column": 37}}, "90": {"start": {"line": 91, "column": 0}, "end": {"line": 91, "column": 3}}, "92": {"start": {"line": 93, "column": 0}, "end": {"line": 93, "column": 46}}, "93": {"start": {"line": 94, "column": 0}, "end": {"line": 94, "column": 57}}, "94": {"start": {"line": 95, "column": 0}, "end": {"line": 95, "column": 31}}, "95": {"start": {"line": 96, "column": 0}, "end": {"line": 96, "column": 28}}, "96": {"start": {"line": 97, "column": 0}, "end": {"line": 97, "column": 6}}, "97": {"start": {"line": 98, "column": 0}, "end": {"line": 98, "column": 19}}, "98": {"start": {"line": 99, "column": 0}, "end": {"line": 99, "column": 3}}, "100": {"start": {"line": 101, "column": 0}, "end": {"line": 101, "column": 60}}, "101": {"start": {"line": 102, "column": 0}, "end": {"line": 102, "column": 57}}, "102": {"start": {"line": 103, "column": 0}, "end": {"line": 103, "column": 41}}, "103": {"start": {"line": 104, "column": 0}, "end": {"line": 104, "column": 28}}, "104": {"start": {"line": 105, "column": 0}, "end": {"line": 105, "column": 6}}, "105": {"start": {"line": 106, "column": 0}, "end": {"line": 106, "column": 19}}, "106": {"start": {"line": 107, "column": 0}, "end": {"line": 107, "column": 3}}, "107": {"start": {"line": 108, "column": 0}, "end": {"line": 108, "column": 1}}}, "s": {"0": 0, "4": 0, "5": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "57": 0, "58": 0, "59": 0, "61": 0, "62": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "71": 0, "72": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "81": 0, "82": 0, "84": 0, "85": 0, "86": 0, "87": 0, "89": 0, "90": 0, "92": 0, "93": 0, "94": 0, "95": 0, "96": 0, "97": 0, "98": 0, "100": 0, "101": 0, "102": 0, "103": 0, "104": 0, "105": 0, "106": 0, "107": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 108, "column": -291}}, "locations": [{"start": {"line": 1, "column": 0}, "end": {"line": 108, "column": -291}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 0}, "end": {"line": 108, "column": -291}}, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 108, "column": -291}}, "line": 1}}, "f": {"0": 0}}, "/Users/<USER>/code/open-trpc/packages/server-core/src/db/repositories/user.repository.ts": {"path": "/Users/<USER>/code/open-trpc/packages/server-core/src/db/repositories/user.repository.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 32}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 29}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 44}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 52}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 56}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 30}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 6}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 25}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 3}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 58}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 56}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 36}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 6}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 25}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 3}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 58}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 56}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 36}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 6}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 25}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 3}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 53}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 34}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 20}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 23}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 18}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 18}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 46}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 5}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 17}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 3}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 81}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 34}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 20}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 20}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 30}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 18}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 25}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 3}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 46}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 32}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 20}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 30}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 37}}, "57": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 3}}, "59": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 46}}, "60": {"start": {"line": 61, "column": 0}, "end": {"line": 61, "column": 56}}, "61": {"start": {"line": 62, "column": 0}, "end": {"line": 62, "column": 30}}, "62": {"start": {"line": 63, "column": 0}, "end": {"line": 63, "column": 28}}, "63": {"start": {"line": 64, "column": 0}, "end": {"line": 64, "column": 6}}, "64": {"start": {"line": 65, "column": 0}, "end": {"line": 65, "column": 19}}, "65": {"start": {"line": 66, "column": 0}, "end": {"line": 66, "column": 3}}, "66": {"start": {"line": 67, "column": 0}, "end": {"line": 67, "column": 1}}}, "s": {"0": 0, "4": 0, "5": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "34": 0, "35": 0, "36": 0, "38": 0, "39": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "48": 0, "49": 0, "51": 0, "52": 0, "53": 0, "54": 0, "56": 0, "57": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 67, "column": -75}}, "locations": [{"start": {"line": 1, "column": 0}, "end": {"line": 67, "column": -75}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 0}, "end": {"line": 67, "column": -75}}, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 67, "column": -75}}, "line": 1}}, "f": {"0": 0}}}