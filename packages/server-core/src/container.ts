import { Redis } from 'ioredis'
import { db, type DatabaseClient } from './db/client'
import {
  UserRepository,
  ApplicationRepository,
  BalanceRepository,
  OrderRepository,
} from './db/repositories'
import {
  AuthService,
  JwtService,
  VerificationService,
  CryptoService,
  ApplicationService,
  BalanceService,
  OrderService,
  UserService,
  SessionTokenService,
} from './core/services'

export interface ContainerConfig {
  jwtSecret: string
  redis: {
    host: string
    port: number
    password?: string
    username?: string
    db?: number
  }
}

export class Container {
  // 基础设施
  public readonly redis: Redis
  public readonly database: DatabaseClient

  // Repositories
  public readonly userRepository: UserRepository
  public readonly applicationRepository: ApplicationRepository
  public readonly balanceRepository: BalanceRepository
  public readonly orderRepository: OrderRepository

  // Services
  public readonly cryptoService: CryptoService
  public readonly jwtService: JwtService
  public readonly verificationService: VerificationService
  public readonly sessionTokenService: SessionTokenService
  public readonly authService: AuthService
  public readonly userService: UserService
  public readonly applicationService: ApplicationService
  public readonly balanceService: BalanceService
  public readonly orderService: OrderService

  constructor(config: ContainerConfig) {
    // 初始化基础设施
    this.redis = new Redis({
      host: config.redis.host,
      port: config.redis.port,
      password: config.redis.password,
      username: config.redis.username,
      db: config.redis.db,
    })

    this.database = db

    // 初始化 Repositories（底层依赖）
    this.userRepository = new UserRepository(this.database)
    this.applicationRepository = new ApplicationRepository(this.database)
    this.balanceRepository = new BalanceRepository(this.database)
    this.orderRepository = new OrderRepository(this.database)

    // 初始化基础 Services
    this.cryptoService = new CryptoService()
    this.jwtService = new JwtService(config.jwtSecret)
    this.verificationService = new VerificationService(this.redis)
    this.sessionTokenService = new SessionTokenService(this.redis, this.cryptoService)

    // 初始化业务 Services（按依赖顺序）
    this.authService = new AuthService(
      this.userRepository,
      this.verificationService,
      this.jwtService
    )

    this.userService = new UserService(
      this.userRepository,
      this.authService
    )

    this.applicationService = new ApplicationService(
      this.applicationRepository,
      this.cryptoService
    )

    this.balanceService = new BalanceService(
      this.balanceRepository
    )

    this.orderService = new OrderService(
      this.orderRepository,
      this.balanceService,
      this.cryptoService
    )
  }

  /**
   * 关闭容器，清理资源
   */
  async close(): Promise<void> {
    await this.redis.quit()
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<{
    redis: boolean
    database: boolean
  }> {
    const results = {
      redis: false,
      database: false,
    }

    try {
      await this.redis.ping()
      results.redis = true
    } catch (error) {
      console.error('Redis health check failed:', error)
    }

    try {
      // 简单的数据库查询测试
      await this.database.query.users.findFirst({
        columns: { id: true },
      })
      results.database = true
    } catch (error) {
      console.error('Database health check failed:', error)
    }

    return results
  }
}

/**
 * 创建容器实例
 */
export function createContainer(config: ContainerConfig): Container {
  return new Container(config)
}
