// 导出主要的 API 路由
export { appRouter, openApiRouter } from './api/_app'
export type { AppRouter, OpenApiRouter } from './api/_app'

// 导出容器
export { Container, createContainer } from './container'
export type { ContainerConfig } from './container'

// 导出 Context 类型
export type { Context } from './api/context'

// 导出中间件
export { protectedProcedure } from './api/middleware/auth.middleware'
export { openProtectedProcedure } from './api/middleware/open-auth.middleware'

// 导出服务类型（用于扩展）
export type {
  AuthService,
  SafeUser,
  JwtService,
  JWTPayload,
  OpenAPIJWTPayload,
  VerificationService,
  VerificationType,
  CryptoService,
  ApplicationService,
  BalanceService,
  OrderService,
  CreateOrderParams,
  OrderListParams,
  UserService,
  SessionTokenService,
  SessionTokenData,
} from './core/services'

// 导出 Repository 类型（用于扩展）
export type {
  UserRepository,
  ApplicationRepository,
  BalanceRepository,
  OrderRepository,
} from './db/repositories'
