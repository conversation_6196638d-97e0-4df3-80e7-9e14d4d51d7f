import { TRPCError } from '@trpc/server'
import { t } from '../trpc'
import { LRUCache } from 'lru-cache'
import type { User } from '@coozf/db/schema'

// 缓存配置
const userCache = new LRUCache<string, User>({
  max: 1000,
  ttl: 5 * 60 * 1000, // 5分钟用户信息缓存
})

export const authMiddleware = t.middleware(async ({ ctx, next }) => {
  // 从cookie或Authorization header获取accessToken
  const accessToken =
    (ctx.req as any).cookies?.['auth-token'] ??
    (ctx.req.headers.authorization?.startsWith('Bearer ')
      ? ctx.req.headers.authorization.slice(7)
      : undefined)

  if (!accessToken) {
    throw new TRPCError({
      code: 'UNAUTHORIZED',
      message: '请先登录或提供有效的访问令牌',
    })
  }

  try {
    // 验证 JWT token
    const payload = ctx.container.jwtService.verifyToken(accessToken)
    
    // 尝试从缓存获取用户信息
    let user = userCache.get(payload.userId)
    
    if (!user) {
      // 缓存中没有，从数据库获取
      const safeUser = await ctx.container.userService.getUserById(payload.userId)
      if (!safeUser) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: '用户不存在',
        })
      }
      
      // 将 SafeUser 转换为 User（添加 password 字段为 null）
      user = { ...safeUser, password: null }
      userCache.set(payload.userId, user)
    }

    return next({
      ctx: {
        ...ctx,
        user,
      },
    })
  } catch (error) {
    throw new TRPCError({
      code: 'UNAUTHORIZED',
      message: '访问令牌无效或已过期',
    })
  }
})

export const protectedProcedure = t.procedure.use(authMiddleware)
