import { TRPCError } from '@trpc/server'
import { t } from '../trpc'
import { LRUCache } from 'lru-cache'
import type { User, Application } from '@coozf/db/schema'

// 缓存用户-应用组合数据
type UserAppData = {
  user: User
  application: Application
}

const userAppCache = new LRUCache<string, UserAppData>({
  max: 1000,
  ttl: 5 * 60 * 1000, // 5分钟缓存
})

// 验证token是否有效
export async function verifyToken(authHeader: string, container: any) {
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new TRPCError({
      code: 'UNAUTHORIZED',
      message: '请提供有效的 Authorization header',
    })
  }

  const token = authHeader.slice(7)

  try {
    const payload = container.jwtService.verifyOpenAPIToken(token)
    const cacheKey = `${payload.userId}:${payload.appId}`
    
    // 尝试从缓存获取
    let userAppData = userAppCache.get(cacheKey)
    
    if (!userAppData) {
      // 从数据库获取用户信息
      const safeUser = await container.userService.getUserById(payload.userId)
      if (!safeUser) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: '用户不存在',
        })
      }

      // 从数据库获取应用信息
      const application = await container.applicationService.getApplicationByAppId(payload.appId)
      if (!application) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: '应用不存在',
        })
      }

      // 验证应用是否属于该用户
      if (application.userId !== payload.userId) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: '应用不属于该用户',
        })
      }

      // 将 SafeUser 转换为 User
      const user = { ...safeUser, password: null }
      
      userAppData = { user, application }
      userAppCache.set(cacheKey, userAppData)
    }

    return userAppData
  } catch (error) {
    if (error instanceof TRPCError) {
      throw error
    }
    throw new TRPCError({
      code: 'UNAUTHORIZED',
      message: 'Token 无效或已过期',
    })
  }
}

export const openAuthMiddleware = t.middleware(async ({ ctx, next }) => {
  const authHeader = ctx.req.headers.authorization
  
  if (!authHeader) {
    throw new TRPCError({
      code: 'UNAUTHORIZED',
      message: '请提供 Authorization header',
    })
  }

  const { user, application } = await verifyToken(authHeader, ctx.container)

  return next({
    ctx: {
      ...ctx,
      user,
      application,
    },
  })
})

export const openProtectedProcedure = t.procedure.use(openAuthMiddleware)
