import { router } from './trpc'
import {
  authRouter,
  userRouter,
  applicationRouter,
  balanceRouter,
  orderRouter,
  openAuthRouter,
} from './routers'

export const appRouter = router({
  auth: authRouter,
  user: userRouter,
  application: applicationRouter,
  balance: balanceRouter,
  order: orderRouter,
})

export const openApiRouter = router({
  openAuth: openAuthRouter,
})

// export type definition of API
export type AppRouter = typeof appRouter
export type OpenApiRouter = typeof openApiRouter
