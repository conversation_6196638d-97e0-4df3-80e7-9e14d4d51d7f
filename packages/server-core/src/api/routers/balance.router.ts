import { z } from 'zod'
import { TRPCError } from '@trpc/server'
import { router } from '../trpc'
import { protectedProcedure } from '../middleware/auth.middleware'

const ApplicationIdSchema = z.object({
  applicationId: z.string().min(1, '应用ID不能为空'),
})

const RechargeSchema = z.object({
  applicationId: z.string().min(1, '应用ID不能为空'),
  amount: z.number().positive('充值金额必须大于0'),
})



export const balanceRouter = router({
  // 获取应用余额
  getBalance: protectedProcedure
    .input(ApplicationIdSchema)
    .query(async ({ input, ctx }) => {
      if (!ctx.user) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: '用户未登录',
        })
      }

      try {
        // 验证应用是否属于用户
        const application = await ctx.container.applicationService.getApplicationById(input.applicationId)
        if (!application || application.userId !== ctx.user.id) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: '无权访问该应用',
          })
        }

        const balance = await ctx.container.balanceService.getApplicationBalance(
          input.applicationId
        )

        return {
          balance,
          applicationId: input.applicationId,
        }
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error
        }
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: error instanceof Error ? error.message : '获取余额失败',
        })
      }
    }),

  // 获取用户所有应用的余额
  getAllBalances: protectedProcedure
    .query(async ({ ctx }) => {
      if (!ctx.user) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: '用户未登录',
        })
      }

      try {
        // 获取用户的所有应用
        const applications = await ctx.container.applicationService.getUserApplications(ctx.user.id)
        const applicationIds = applications.map(app => app.id)

        // 获取这些应用的余额
        const balances = await ctx.container.balanceService.getApplicationBalances(applicationIds)
        return balances
      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: error instanceof Error ? error.message : '获取余额列表失败',
        })
      }
    }),

  // 充值余额（管理员功能，这里简化为用户自己充值）
  recharge: protectedProcedure
    .input(RechargeSchema)
    .mutation(async ({ input, ctx }) => {
      if (!ctx.user) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: '用户未登录',
        })
      }

      try {
        // 验证应用是否存在且属于用户
        const application = await ctx.container.applicationService.getApplicationById(input.applicationId)
        if (!application) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: '应用不存在',
          })
        }

        if (application.userId !== ctx.user.id) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: '无权操作该应用',
          })
        }

        const updatedBalance = await ctx.container.balanceService.rechargeBalance(
          input.applicationId,
          input.amount
        )

        return {
          message: '充值成功',
          balance: updatedBalance,
        }
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error
        }
        
        if (error instanceof Error && error.message === 'BALANCE_UPDATE_FAILED') {
          throw new TRPCError({
            code: 'INTERNAL_SERVER_ERROR',
            message: '余额更新失败',
          })
        }
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: error instanceof Error ? error.message : '充值失败',
        })
      }
    }),

  // 检查余额是否足够
  checkBalance: protectedProcedure
    .input(z.object({
      applicationId: z.string().min(1, '应用ID不能为空'),
      amount: z.number().positive('金额必须大于0'),
    }))
    .query(async ({ input, ctx }) => {
      if (!ctx.user) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: '用户未登录',
        })
      }

      try {
        // 验证应用是否属于用户
        const application = await ctx.container.applicationService.getApplicationById(input.applicationId)
        if (!application || application.userId !== ctx.user.id) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: '无权访问该应用',
          })
        }

        const hasEnoughBalance = await ctx.container.balanceService.hasEnoughBalance(
          input.applicationId,
          input.amount
        )

        return {
          hasEnoughBalance,
          requiredAmount: input.amount,
        }
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error
        }
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: error instanceof Error ? error.message : '检查余额失败',
        })
      }
    }),
})
