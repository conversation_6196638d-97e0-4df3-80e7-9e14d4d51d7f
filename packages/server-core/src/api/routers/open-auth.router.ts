import { z } from 'zod'
import { TRPCError } from '@trpc/server'
import { router, publicProcedure } from '../trpc'

// 认证请求 Schema
const AuthenticateSchema = z.object({
  appId: z.string().min(1, 'App ID 不能为空').describe('应用唯一标识符，格式：app_xxxxxxxx'),
  secret: z.string().min(1, 'Secret 不能为空').describe('应用密钥，格式：sk_xxxxxxxx'),
})

// Session Token 生成 Schema
const GenerateSessionTokenSchema = z.object({
  appId: z.string().min(1, 'App ID 不能为空'),
  secret: z.string().min(1, 'Secret 不能为空'),
})

export const openAuthRouter = router({
  // 应用认证，获取 API Token
  authenticate: publicProcedure
    .input(AuthenticateSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        // 验证应用密钥
        const application = await ctx.container.applicationService.verifyApplicationSecret(
          input.appId,
          input.secret
        )

        if (!application) {
          throw new TRPCError({
            code: 'UNAUTHORIZED',
            message: 'App ID 或 Secret 错误',
          })
        }

        // 检查应用状态
        if (application.status !== 'active') {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: '应用已被暂停或删除',
          })
        }

        // 生成开放平台 API Token
        const token = ctx.container.jwtService.generateOpenAPIToken({
          userId: application.userId,
          appId: application.appId,
          type: 'open_api',
        })

        return {
          message: '认证成功',
          token,
          application: {
            id: application.id,
            appId: application.appId,
            name: application.name,
            status: application.status,
          },
        }
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error
        }
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: error instanceof Error ? error.message : '认证失败',
        })
      }
    }),

  // 生成 Session Token
  generateSessionToken: publicProcedure
    .input(GenerateSessionTokenSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        // 验证应用密钥
        const application = await ctx.container.applicationService.verifyApplicationSecret(
          input.appId,
          input.secret
        )

        if (!application) {
          throw new TRPCError({
            code: 'UNAUTHORIZED',
            message: 'App ID 或 Secret 错误',
          })
        }

        // 检查应用状态
        if (application.status !== 'active') {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: '应用已被暂停或删除',
          })
        }

        // 生成 Session Token
        const sessionToken = await ctx.container.sessionTokenService.generateSessionToken(
          application.userId,
          application.id
        )

        return {
          message: 'Session Token 生成成功',
          sessionToken,
          expiresIn: 3600, // 1小时
        }
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error
        }
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: error instanceof Error ? error.message : 'Session Token 生成失败',
        })
      }
    }),

  // 验证 Session Token
  verifySessionToken: publicProcedure
    .input(z.object({
      sessionToken: z.string().min(1, 'Session Token 不能为空'),
    }))
    .query(async ({ input, ctx }) => {
      try {
        const tokenData = await ctx.container.sessionTokenService.verifySessionToken(
          input.sessionToken
        )

        if (!tokenData) {
          throw new TRPCError({
            code: 'UNAUTHORIZED',
            message: 'Session Token 无效或已过期',
          })
        }

        // 获取用户信息
        const user = await ctx.container.userService.getUserById(tokenData.userId)
        if (!user) {
          throw new TRPCError({
            code: 'UNAUTHORIZED',
            message: '用户不存在',
          })
        }

        // 获取应用信息
        const application = await ctx.container.applicationService.getApplicationById(
          tokenData.applicationId
        )
        if (!application) {
          throw new TRPCError({
            code: 'UNAUTHORIZED',
            message: '应用不存在',
          })
        }

        return {
          valid: true,
          user: {
            id: user.id,
            name: user.name,
            email: user.email,
            phone: user.phone,
            avatar: user.avatar,
          },
          application: {
            id: application.id,
            appId: application.appId,
            name: application.name,
          },
          createdAt: tokenData.createdAt,
        }
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error
        }
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: error instanceof Error ? error.message : 'Session Token 验证失败',
        })
      }
    }),

  // 刷新 Session Token
  refreshSessionToken: publicProcedure
    .input(z.object({
      sessionToken: z.string().min(1, 'Session Token 不能为空'),
    }))
    .mutation(async ({ input, ctx }) => {
      try {
        const success = await ctx.container.sessionTokenService.refreshSessionToken(
          input.sessionToken
        )

        if (!success) {
          throw new TRPCError({
            code: 'UNAUTHORIZED',
            message: 'Session Token 无效或已过期',
          })
        }

        return {
          message: 'Session Token 刷新成功',
          expiresIn: 3600, // 1小时
        }
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error
        }
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: error instanceof Error ? error.message : 'Session Token 刷新失败',
        })
      }
    }),

  // 撤销 Session Token
  revokeSessionToken: publicProcedure
    .input(z.object({
      sessionToken: z.string().min(1, 'Session Token 不能为空'),
    }))
    .mutation(async ({ input, ctx }) => {
      try {
        const success = await ctx.container.sessionTokenService.revokeSessionToken(
          input.sessionToken
        )

        return {
          message: success ? 'Session Token 撤销成功' : 'Session Token 不存在',
          revoked: success,
        }
      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: error instanceof Error ? error.message : 'Session Token 撤销失败',
        })
      }
    }),
})
