import { z } from 'zod'
import { TRPCError } from '@trpc/server'
import { router } from '../trpc'
import { protectedProcedure } from '../middleware/auth.middleware'

const CreateOrderSchema = z.object({
  applicationId: z.string().min(1, '应用ID不能为空'),
  amount: z.number().positive('金额必须大于0'),
  type: z.enum(['GIFT', 'PURCHASE']).default('GIFT'),
  remarks: z.string().max(500, '备注不能超过500个字符').optional(),
})

const OrderListSchema = z.object({
  page: z.number().int().positive().default(1),
  pageSize: z.number().int().positive().max(100).default(10),
  applicationId: z.string().optional(),
  status: z.string().optional(),
  type: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  search: z.string().optional(),
})

const OrderIdSchema = z.object({
  orderId: z.string().min(1, '订单ID不能为空'),
})

const OrderNoSchema = z.object({
  orderNo: z.string().min(1, '订单号不能为空'),
})

export const orderRouter = router({
  // 创建充值订单
  create: protectedProcedure
    .input(CreateOrderSchema)
    .mutation(async ({ input, ctx }) => {
      if (!ctx.user) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: '用户未登录',
        })
      }

      try {
        // 验证应用是否存在且属于用户
        const application = await ctx.container.applicationService.getApplicationById(input.applicationId)
        if (!application) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: '应用不存在',
          })
        }

        if (application.userId !== ctx.user.id) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: '无权操作该应用',
          })
        }

        const orderNo = await ctx.container.orderService.createRechargeOrder({
          userId: ctx.user.id,
          applicationId: input.applicationId,
          amount: input.amount,
          type: input.type,
          remarks: input.remarks,
        })

        return {
          message: '订单创建成功',
          orderNo,
        }
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error
        }
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: error instanceof Error ? error.message : '订单创建失败',
        })
      }
    }),

  // 获取订单列表
  list: protectedProcedure
    .input(OrderListSchema)
    .query(async ({ input, ctx }) => {
      if (!ctx.user) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: '用户未登录',
        })
      }

      try {
        const orders = await ctx.container.orderService.getOrderList({
          ...input,
          userId: ctx.user.id, // 只能查看自己的订单
        })

        return orders
      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: error instanceof Error ? error.message : '获取订单列表失败',
        })
      }
    }),

  // 获取订单详情
  detail: protectedProcedure
    .input(OrderIdSchema)
    .query(async ({ input, ctx }) => {
      if (!ctx.user) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: '用户未登录',
        })
      }

      try {
        const order = await ctx.container.orderService.getOrderById(input.orderId)
        
        if (!order) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: '订单不存在',
          })
        }

        // 验证订单是否属于当前用户
        if (order.userId !== ctx.user.id) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: '无权访问该订单',
          })
        }

        return order
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error
        }
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: error instanceof Error ? error.message : '获取订单详情失败',
        })
      }
    }),

  // 根据订单号获取订单
  getByOrderNo: protectedProcedure
    .input(OrderNoSchema)
    .query(async ({ input, ctx }) => {
      if (!ctx.user) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: '用户未登录',
        })
      }

      try {
        const order = await ctx.container.orderService.getOrderByOrderNo(input.orderNo)
        
        if (!order) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: '订单不存在',
          })
        }

        // 验证订单是否属于当前用户
        if (order.userId !== ctx.user.id) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: '无权访问该订单',
          })
        }

        return order
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error
        }
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: error instanceof Error ? error.message : '获取订单失败',
        })
      }
    }),

  // 取消订单
  cancel: protectedProcedure
    .input(OrderIdSchema)
    .mutation(async ({ input, ctx }) => {
      if (!ctx.user) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: '用户未登录',
        })
      }

      try {
        const order = await ctx.container.orderService.getOrderById(input.orderId)
        
        if (!order) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: '订单不存在',
          })
        }

        // 验证订单是否属于当前用户
        if (order.userId !== ctx.user.id) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: '无权操作该订单',
          })
        }

        const cancelledOrder = await ctx.container.orderService.cancelOrder(input.orderId)

        if (!cancelledOrder) {
          throw new TRPCError({
            code: 'INTERNAL_SERVER_ERROR',
            message: '订单取消失败',
          })
        }

        return {
          message: '订单取消成功',
          order: cancelledOrder,
        }
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error
        }
        
        if (error instanceof Error) {
          if (error.message === 'ORDER_NOT_FOUND') {
            throw new TRPCError({
              code: 'NOT_FOUND',
              message: '订单不存在',
            })
          }
          if (error.message === 'CANNOT_CANCEL_COMPLETED_ORDER') {
            throw new TRPCError({
              code: 'BAD_REQUEST',
              message: '已完成的订单无法取消',
            })
          }
        }
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: error instanceof Error ? error.message : '订单取消失败',
        })
      }
    }),

  // 申请发票
  requestInvoice: protectedProcedure
    .input(OrderIdSchema)
    .mutation(async ({ input, ctx }) => {
      if (!ctx.user) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: '用户未登录',
        })
      }

      try {
        const order = await ctx.container.orderService.getOrderById(input.orderId)
        
        if (!order) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: '订单不存在',
          })
        }

        // 验证订单是否属于当前用户
        if (order.userId !== ctx.user.id) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: '无权操作该订单',
          })
        }

        const updatedOrder = await ctx.container.orderService.requestInvoice(input.orderId)

        if (!updatedOrder) {
          throw new TRPCError({
            code: 'INTERNAL_SERVER_ERROR',
            message: '发票申请失败',
          })
        }

        return {
          message: '发票申请成功',
          order: updatedOrder,
        }
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error
        }
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: error instanceof Error ? error.message : '发票申请失败',
        })
      }
    }),

  // 获取用户订单统计
  stats: protectedProcedure
    .query(async ({ ctx }) => {
      if (!ctx.user) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: '用户未登录',
        })
      }

      try {
        const stats = await ctx.container.orderService.getUserOrderStats(ctx.user.id)
        return stats
      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: error instanceof Error ? error.message : '获取订单统计失败',
        })
      }
    }),
})
