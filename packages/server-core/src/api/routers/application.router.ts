import { z } from 'zod'
import { TRPCError } from '@trpc/server'
import { router } from '../trpc'
import { protectedProcedure } from '../middleware/auth.middleware'

const CreateApplicationSchema = z.object({
  name: z.string().min(1, '应用名称不能为空').max(100, '应用名称不能超过100个字符'),
  description: z.string().max(500, '应用描述不能超过500个字符').optional(),
  webhookUrl: z.string().url('请输入有效的Webhook URL').optional(),
})

const UpdateApplicationSchema = z.object({
  id: z.string().min(1, '应用ID不能为空'),
  name: z.string().min(1, '应用名称不能为空').max(100, '应用名称不能超过100个字符').optional(),
  description: z.string().max(500, '应用描述不能超过500个字符').optional(),
  webhookUrl: z.string().url('请输入有效的Webhook URL').optional(),
  status: z.enum(['active', 'suspended', 'deleted']).optional(),
})

const ApplicationIdSchema = z.object({
  id: z.string().min(1, '应用ID不能为空'),
})

export const applicationRouter = router({
  // 创建应用
  create: protectedProcedure
    .input(CreateApplicationSchema)
    .mutation(async ({ input, ctx }) => {
      if (!ctx.user) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: '用户未登录',
        })
      }

      try {
        const result = await ctx.container.applicationService.createApplication(
          ctx.user.id,
          input.name,
          input.description,
          input.webhookUrl
        )

        return {
          message: '应用创建成功',
          application: result.application,
          secret: result.secret,
        }
      } catch (error) {
        if (error instanceof Error && error.message === 'APPID_GENERATION_FAILED') {
          throw new TRPCError({
            code: 'INTERNAL_SERVER_ERROR',
            message: '应用ID生成失败，请重试',
          })
        }
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: error instanceof Error ? error.message : '应用创建失败',
        })
      }
    }),

  // 获取用户的应用列表
  list: protectedProcedure
    .query(async ({ ctx }) => {
      if (!ctx.user) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: '用户未登录',
        })
      }

      try {
        const applications = await ctx.container.applicationService.getUserApplications(ctx.user.id)
        return applications
      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: error instanceof Error ? error.message : '获取应用列表失败',
        })
      }
    }),

  // 获取应用详情
  detail: protectedProcedure
    .input(ApplicationIdSchema)
    .query(async ({ input, ctx }) => {
      if (!ctx.user) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: '用户未登录',
        })
      }

      try {
        const application = await ctx.container.applicationService.getApplicationById(input.id)
        
        if (!application) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: '应用不存在',
          })
        }

        // 验证应用是否属于当前用户
        if (application.userId !== ctx.user.id) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: '无权访问该应用',
          })
        }

        return application
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error
        }
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: error instanceof Error ? error.message : '获取应用详情失败',
        })
      }
    }),

  // 更新应用
  update: protectedProcedure
    .input(UpdateApplicationSchema)
    .mutation(async ({ input, ctx }) => {
      if (!ctx.user) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: '用户未登录',
        })
      }

      try {
        const { id, ...updates } = input
        const updatedApplication = await ctx.container.applicationService.updateApplication(
          id,
          ctx.user.id,
          updates
        )

        if (!updatedApplication) {
          throw new TRPCError({
            code: 'INTERNAL_SERVER_ERROR',
            message: '应用更新失败',
          })
        }

        return {
          message: '应用更新成功',
          application: updatedApplication,
        }
      } catch (error) {
        if (error instanceof Error && error.message === 'APPLICATION_NOT_FOUND') {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: '应用不存在或无权访问',
          })
        }
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: error instanceof Error ? error.message : '应用更新失败',
        })
      }
    }),

  // 重新生成密钥
  regenerateSecret: protectedProcedure
    .input(ApplicationIdSchema)
    .mutation(async ({ input, ctx }) => {
      if (!ctx.user) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: '用户未登录',
        })
      }

      try {
        const result = await ctx.container.applicationService.regenerateSecret(
          input.id,
          ctx.user.id
        )

        return {
          message: '密钥重新生成成功',
          application: result.application,
          secret: result.secret,
        }
      } catch (error) {
        if (error instanceof Error) {
          if (error.message === 'APPLICATION_NOT_FOUND') {
            throw new TRPCError({
              code: 'NOT_FOUND',
              message: '应用不存在或无权访问',
            })
          }
          if (error.message === 'SECRET_REGENERATION_FAILED') {
            throw new TRPCError({
              code: 'INTERNAL_SERVER_ERROR',
              message: '密钥重新生成失败',
            })
          }
        }
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: error instanceof Error ? error.message : '密钥重新生成失败',
        })
      }
    }),

  // 删除应用
  delete: protectedProcedure
    .input(ApplicationIdSchema)
    .mutation(async ({ input, ctx }) => {
      if (!ctx.user) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: '用户未登录',
        })
      }

      try {
        const success = await ctx.container.applicationService.deleteApplication(
          input.id,
          ctx.user.id
        )

        if (!success) {
          throw new TRPCError({
            code: 'INTERNAL_SERVER_ERROR',
            message: '应用删除失败',
          })
        }

        return {
          message: '应用删除成功',
        }
      } catch (error) {
        if (error instanceof Error && error.message === 'APPLICATION_NOT_FOUND') {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: '应用不存在或无权访问',
          })
        }
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: error instanceof Error ? error.message : '应用删除失败',
        })
      }
    }),
})
