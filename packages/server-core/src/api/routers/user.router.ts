import { z } from 'zod'
import { TRPCError } from '@trpc/server'
import { router } from '../trpc'
import { protectedProcedure } from '../middleware/auth.middleware'

const UpdateProfileSchema = z.object({
  name: z.string().min(1, '姓名不能为空').optional(),
  email: z.string().email('请输入有效的邮箱地址').optional(),
  phone: z.string().regex(/^1[3-9]\d{9}$/, '请输入有效的手机号').optional(),
})

const UpdatePasswordSchema = z.object({
  oldPassword: z.string().min(6, '旧密码至少6位'),
  newPassword: z.string().min(6, '新密码至少6位'),
})

const SetPasswordSchema = z.object({
  password: z.string().min(6, '密码至少6位'),
})

const UpdateAvatarSchema = z.object({
  avatarUrl: z.string().url('请输入有效的头像URL'),
})

export const userRouter = router({
  // 获取当前用户信息
  me: protectedProcedure
    .query(async ({ ctx }) => {
      if (!ctx.user) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: '用户未登录',
        })
      }

      const { password: _, ...safeUser } = ctx.user
      return safeUser
    }),

  // 更新用户基本信息
  updateProfile: protectedProcedure
    .input(UpdateProfileSchema)
    .mutation(async ({ input, ctx }) => {
      if (!ctx.user) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: '用户未登录',
        })
      }

      try {
        const updatedUser = await ctx.container.userService.updateProfile(
          ctx.user.id,
          input
        )

        if (!updatedUser) {
          throw new TRPCError({
            code: 'INTERNAL_SERVER_ERROR',
            message: '更新失败',
          })
        }

        return {
          message: '更新成功',
          user: updatedUser,
        }
      } catch (error) {
        if (error instanceof Error) {
          if (error.message === 'EMAIL_ALREADY_EXISTS') {
            throw new TRPCError({
              code: 'BAD_REQUEST',
              message: '邮箱已被其他用户使用',
            })
          }
          if (error.message === 'PHONE_ALREADY_EXISTS') {
            throw new TRPCError({
              code: 'BAD_REQUEST',
              message: '手机号已被其他用户使用',
            })
          }
        }
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: error instanceof Error ? error.message : '更新失败',
        })
      }
    }),

  // 更新密码
  updatePassword: protectedProcedure
    .input(UpdatePasswordSchema)
    .mutation(async ({ input, ctx }) => {
      if (!ctx.user) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: '用户未登录',
        })
      }

      try {
        const success = await ctx.container.userService.updatePassword(
          ctx.user.id,
          input.oldPassword,
          input.newPassword
        )

        if (!success) {
          throw new TRPCError({
            code: 'INTERNAL_SERVER_ERROR',
            message: '密码更新失败',
          })
        }

        return {
          message: '密码更新成功',
        }
      } catch (error) {
        if (error instanceof Error) {
          if (error.message === 'USER_NOT_FOUND') {
            throw new TRPCError({
              code: 'NOT_FOUND',
              message: '用户不存在',
            })
          }
          if (error.message === 'INVALID_OLD_PASSWORD') {
            throw new TRPCError({
              code: 'BAD_REQUEST',
              message: '旧密码错误',
            })
          }
        }
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: error instanceof Error ? error.message : '密码更新失败',
        })
      }
    }),

  // 设置密码（首次设置）
  setPassword: protectedProcedure
    .input(SetPasswordSchema)
    .mutation(async ({ input, ctx }) => {
      if (!ctx.user) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: '用户未登录',
        })
      }

      try {
        const success = await ctx.container.userService.setPassword(
          ctx.user.id,
          input.password
        )

        if (!success) {
          throw new TRPCError({
            code: 'INTERNAL_SERVER_ERROR',
            message: '密码设置失败',
          })
        }

        return {
          message: '密码设置成功',
        }
      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: error instanceof Error ? error.message : '密码设置失败',
        })
      }
    }),

  // 更新头像
  updateAvatar: protectedProcedure
    .input(UpdateAvatarSchema)
    .mutation(async ({ input, ctx }) => {
      if (!ctx.user) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: '用户未登录',
        })
      }

      try {
        const updatedUser = await ctx.container.userService.updateAvatar(
          ctx.user.id,
          input.avatarUrl
        )

        if (!updatedUser) {
          throw new TRPCError({
            code: 'INTERNAL_SERVER_ERROR',
            message: '头像更新失败',
          })
        }

        return {
          message: '头像更新成功',
          user: updatedUser,
        }
      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: error instanceof Error ? error.message : '头像更新失败',
        })
      }
    }),
})
