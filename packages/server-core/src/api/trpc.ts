import { initTRPC, TRPCError } from '@trpc/server'
import superjson from 'superjson'
import { ZodError } from 'zod'
import type { Context } from './context'

export const t = initTRPC
  .context<Context>()
  .create({
    transformer: superjson,
    errorFormatter(opts) {
      const { shape, error, ctx } = opts

      if (ctx?.isPublicApi) {
        if (error.cause instanceof ZodError) {
          const validationErrors = error.cause.flatten().fieldErrors
          const firstError = Object.values(validationErrors)[0]?.[0] || 'Validation failed'
          return {
            code: 40001,
            message: firstError,
            data: null,
          } as any
        }

        if (error instanceof TRPCError) {
          let code = 50000
          switch (error.code) {
            case 'BAD_REQUEST':
              code = 40000
              break
            case 'UNAUTHORIZED':
              code = 40100
              break
            case 'FORBIDDEN':
              code = 40300
              break
            case 'NOT_FOUND':
              code = 40400
              break
            case 'INTERNAL_SERVER_ERROR':
              code = 50000
              break
          }
          return {
            code,
            message: error.message,
            data: null,
          } as any
        }

        return {
          code: 50000,
          message: 'Internal Server Error',
          data: null,
        } as any
      }

      return {
        ...shape,
        data: {
          ...shape.data,
          zodError:
            error.cause instanceof ZodError
              ? error.cause.flatten()
              : null,
        },
      }
    },
  })

export const router = t.router
export const publicProcedure = t.procedure
