import type { ApplicationBalance, InsertApplicationBalance } from '@coozf/db/schema'
import type { BalanceRepository } from '../../db/repositories/balance.repository'

export class BalanceService {
  constructor(private balanceRepository: BalanceRepository) {}

  /**
   * 获取应用余额
   */
  async getApplicationBalance(applicationId: string): Promise<string> {
    const balance = await this.balanceRepository.findByApplicationId(applicationId)

    if (!balance) {
      // 如果没有余额记录，创建一个初始余额为0的记录
      await this.balanceRepository.create({
        applicationId,
        balance: '0.00',
      })
      return '0.00'
    }

    return balance.balance
  }

  /**
   * 获取多个应用的余额
   */
  async getApplicationBalances(applicationIds: string[]): Promise<ApplicationBalance[]> {
    return this.balanceRepository.findByApplicationIds(applicationIds)
  }

  /**
   * 充值余额
   */
  async rechargeBalance(
    applicationId: string,
    amount: number
  ): Promise<ApplicationBalance> {
    const currentBalance = await this.getApplicationBalance(applicationId)
    const newBalance = (parseFloat(currentBalance) + amount).toFixed(2)

    // 检查是否已存在余额记录
    const existingBalance = await this.balanceRepository.findByApplicationId(applicationId)

    if (existingBalance) {
      const updatedBalance = await this.balanceRepository.updateBalance(applicationId, newBalance)
      if (!updatedBalance) {
        throw new Error('BALANCE_UPDATE_FAILED')
      }
      return updatedBalance
    } else {
      return this.balanceRepository.create({
        applicationId,
        balance: newBalance,
      })
    }
  }

  /**
   * 扣减余额
   */
  async deductBalance(
    applicationId: string,
    amount: number
  ): Promise<ApplicationBalance> {
    const currentBalance = await this.getApplicationBalance(applicationId)
    const currentAmount = parseFloat(currentBalance)

    if (currentAmount < amount) {
      throw new Error('INSUFFICIENT_BALANCE')
    }

    const newBalance = (currentAmount - amount).toFixed(2)
    const updatedBalance = await this.balanceRepository.updateBalance(applicationId, newBalance)

    if (!updatedBalance) {
      throw new Error('BALANCE_UPDATE_FAILED')
    }

    return updatedBalance
  }

  /**
   * 设置余额
   */
  async setBalance(
    applicationId: string,
    balance: string
  ): Promise<ApplicationBalance> {
    const existingBalance = await this.balanceRepository.findByApplicationId(applicationId)

    if (existingBalance) {
      const updatedBalance = await this.balanceRepository.updateBalance(applicationId, balance)
      if (!updatedBalance) {
        throw new Error('BALANCE_UPDATE_FAILED')
      }
      return updatedBalance
    } else {
      return this.balanceRepository.create({
        applicationId,
        balance,
      })
    }
  }

  /**
   * 检查余额是否足够
   */
  async hasEnoughBalance(
    applicationId: string,
    requiredAmount: number
  ): Promise<boolean> {
    const currentBalance = await this.getApplicationBalance(applicationId)
    return parseFloat(currentBalance) >= requiredAmount
  }
}
