import type { Redis } from 'ioredis'

// 验证码数据结构
interface VerificationData {
  code: string
  attempts: number
  createdAt: number
}

// 验证码类型
export type VerificationType = 'login' | 'register' | 'reset_password'

export class VerificationService {
  private static readonly PREFIX = 'verification:'
  private static readonly DEFAULT_EXPIRE = 300 // 5分钟过期
  private static readonly MAX_ATTEMPTS = 5 // 最大尝试次数

  constructor(private redis: Redis) {}

  /**
   * 生成并存储验证码
   */
  async generateCode(
    identifier: string, 
    type: VerificationType
  ): Promise<string> {
    const code = Math.random().toString().slice(2, 8) // 生成6位随机数字
    const key = `${VerificationService.PREFIX}${type}:${identifier}`
    
    // 存储验证码信息
    const data: VerificationData = {
      code,
      attempts: 0,
      createdAt: Date.now()
    }
    
    await this.redis.setex(key, VerificationService.DEFAULT_EXPIRE, JSON.stringify(data))
    return code
  }

  /**
   * 验证验证码
   */
  async verifyCode(
    identifier: string,
    type: VerificationType,
    inputCode: string
  ): Promise<{ success: boolean; message: string }> {
    const key = `${VerificationService.PREFIX}${type}:${identifier}`
    const dataStr = await this.redis.get(key)
    
    if (!dataStr) {
      return { success: false, message: '验证码已过期或不存在' }
    }
    
    const data = JSON.parse(dataStr) as VerificationData
    
    // 检查尝试次数
    if (data.attempts >= VerificationService.MAX_ATTEMPTS) {
      await this.redis.del(key) // 删除已超过尝试次数的验证码
      return { success: false, message: '验证码尝试次数过多，请重新获取' }
    }
    
    // 验证码错误，增加尝试次数
    if (data.code !== inputCode) {
      data.attempts += 1
      await this.redis.setex(key, VerificationService.DEFAULT_EXPIRE, JSON.stringify(data))
      return { 
        success: false, 
        message: `验证码错误，还可尝试${VerificationService.MAX_ATTEMPTS - data.attempts}次` 
      }
    }
    
    // 验证成功，删除验证码
    await this.redis.del(key)
    return { success: true, message: '验证成功' }
  }

  /**
   * 检查验证码发送频率限制
   */
  async checkSendRate(
    identifier: string
  ): Promise<{ canSend: boolean; remainingTime?: number }> {
    const rateKey = `${VerificationService.PREFIX}rate:${identifier}`
    const lastSent = await this.redis.get(rateKey)
    
    if (lastSent) {
      const elapsed = Date.now() - parseInt(lastSent)
      const waitTime = 60000 - elapsed // 1分钟间隔
      
      if (waitTime > 0) {
        return { canSend: false, remainingTime: Math.ceil(waitTime / 1000) }
      }
    }
    
    // 记录发送时间
    await this.redis.setex(rateKey, 60, Date.now().toString())
    return { canSend: true }
  }

  /**
   * 清除验证码
   */
  async clearCode(
    identifier: string,
    type: VerificationType
  ): Promise<void> {
    const key = `${VerificationService.PREFIX}${type}:${identifier}`
    await this.redis.del(key)
  }

  /**
   * 获取验证码剩余有效时间
   */
  async getCodeTTL(
    identifier: string,
    type: VerificationType
  ): Promise<number> {
    const key = `${VerificationService.PREFIX}${type}:${identifier}`
    return await this.redis.ttl(key)
  }
}
