import type { Redis } from 'ioredis'
import type { CryptoService } from './crypto.service'

export interface SessionTokenData {
  userId: string
  applicationId: string
  createdAt: number
}

export class SessionTokenService {
  private static readonly PREFIX = 'session_token:'
  private static readonly DEFAULT_EXPIRE = 3600 // 1小时过期

  constructor(
    private redis: Redis,
    private cryptoService: CryptoService
  ) {}

  /**
   * 生成 Session Token
   */
  async generateSessionToken(userId: string, applicationId: string): Promise<string> {
    const sessionToken = this.cryptoService.generateSessionToken()
    const redisKey = `${SessionTokenService.PREFIX}${sessionToken}`

    const tokenData: SessionTokenData = {
      userId,
      applicationId,
      createdAt: Date.now(),
    }

    await this.redis.set(
      redisKey, 
      JSON.stringify(tokenData), 
      'EX', 
      SessionTokenService.DEFAULT_EXPIRE
    )

    return sessionToken
  }

  /**
   * 验证 Session Token
   */
  async verifySessionToken(sessionToken: string): Promise<SessionTokenData | null> {
    const redisKey = `${SessionTokenService.PREFIX}${sessionToken}`
    const dataStr = await this.redis.get(redisKey)

    if (!dataStr) {
      return null
    }

    try {
      return JSON.parse(dataStr) as SessionTokenData
    } catch (error) {
      return null
    }
  }

  /**
   * 刷新 Session Token 过期时间
   */
  async refreshSessionToken(sessionToken: string): Promise<boolean> {
    const redisKey = `${SessionTokenService.PREFIX}${sessionToken}`
    const exists = await this.redis.exists(redisKey)

    if (!exists) {
      return false
    }

    await this.redis.expire(redisKey, SessionTokenService.DEFAULT_EXPIRE)
    return true
  }

  /**
   * 删除 Session Token
   */
  async revokeSessionToken(sessionToken: string): Promise<boolean> {
    const redisKey = `${SessionTokenService.PREFIX}${sessionToken}`
    const result = await this.redis.del(redisKey)
    return result > 0
  }

  /**
   * 获取 Session Token 剩余有效时间
   */
  async getSessionTokenTTL(sessionToken: string): Promise<number> {
    const redisKey = `${SessionTokenService.PREFIX}${sessionToken}`
    return await this.redis.ttl(redisKey)
  }

  /**
   * 清除用户的所有 Session Token
   */
  async revokeUserSessions(userId: string): Promise<number> {
    const pattern = `${SessionTokenService.PREFIX}*`
    const keys = await this.redis.keys(pattern)
    
    let revokedCount = 0
    
    for (const key of keys) {
      const dataStr = await this.redis.get(key)
      if (dataStr) {
        try {
          const data = JSON.parse(dataStr) as SessionTokenData
          if (data.userId === userId) {
            await this.redis.del(key)
            revokedCount++
          }
        } catch (error) {
          // 忽略解析错误的数据
        }
      }
    }
    
    return revokedCount
  }

  /**
   * 清除应用的所有 Session Token
   */
  async revokeApplicationSessions(applicationId: string): Promise<number> {
    const pattern = `${SessionTokenService.PREFIX}*`
    const keys = await this.redis.keys(pattern)
    
    let revokedCount = 0
    
    for (const key of keys) {
      const dataStr = await this.redis.get(key)
      if (dataStr) {
        try {
          const data = JSON.parse(dataStr) as SessionTokenData
          if (data.applicationId === applicationId) {
            await this.redis.del(key)
            revokedCount++
          }
        } catch (error) {
          // 忽略解析错误的数据
        }
      }
    }
    
    return revokedCount
  }

  /**
   * 获取用户的活跃 Session Token 数量
   */
  async getUserActiveSessionCount(userId: string): Promise<number> {
    const pattern = `${SessionTokenService.PREFIX}*`
    const keys = await this.redis.keys(pattern)
    
    let count = 0
    
    for (const key of keys) {
      const dataStr = await this.redis.get(key)
      if (dataStr) {
        try {
          const data = JSON.parse(dataStr) as SessionTokenData
          if (data.userId === userId) {
            count++
          }
        } catch (error) {
          // 忽略解析错误的数据
        }
      }
    }
    
    return count
  }
}
