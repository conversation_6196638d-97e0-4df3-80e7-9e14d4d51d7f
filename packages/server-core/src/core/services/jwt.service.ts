import jwt from 'jsonwebtoken'

const JWT_EXPIRES_IN = '7Day'
const OPEN_API_EXPIRES_IN = '30d'

export interface JWTPayload {
  userId: string
  phone: string
}

export interface OpenAPIJWTPayload {
  userId: string
  appId: string
  type: 'open_api'
}

export class JwtService {
  constructor(private jwtSecret: string) {}

  /**
   * 生成JWT token
   */
  generateToken(payload: JWTPayload): string {
    return jwt.sign(payload, this.jwtSecret, {
      expiresIn: JWT_EXPIRES_IN,
    })
  }

  /**
   * 生成开放平台 API token
   */
  generateOpenAPIToken(payload: OpenAPIJWTPayload): string {
    return jwt.sign(payload, this.jwtSecret, {
      expiresIn: OPEN_API_EXPIRES_IN,
    })
  }

  /**
   * 验证JWT token
   */
  verifyToken(token: string): JWTPayload {
    try {
      return jwt.verify(token, this.jwtSecret) as JWTPayload
    } catch (error) {
      throw new Error('TOKEN_INVALID')
    }
  }

  /**
   * 验证开放平台 API token
   */
  verifyOpenAPIToken(token: string): OpenAPIJWTPayload {
    try {
      const payload = jwt.verify(token, this.jwtSecret) as OpenAPIJWTPayload
      if (payload.type !== 'open_api') {
        throw new Error('INVALID_TOKEN_TYPE')
      }
      return payload
    } catch (error) {
      throw new Error('TOKEN_INVALID')
    }
  }

  /**
   * 解码JWT token（不验证）
   */
  decodeToken(token: string): any {
    return jwt.decode(token)
  }
}
