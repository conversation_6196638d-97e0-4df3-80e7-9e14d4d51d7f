import type { User, InsertUser } from '@coozf/db/schema'
import type { UserRepository } from '../../db/repositories/user.repository'
import type { AuthService, SafeUser } from './auth.service'

export class UserService {
  constructor(
    private userRepository: UserRepository,
    private authService: AuthService
  ) {}

  /**
   * 根据ID获取用户（安全版本，不包含密码）
   */
  async getUserById(id: string): Promise<SafeUser | null> {
    const user = await this.userRepository.findById(id)
    if (!user) {
      return null
    }
    
    const { password: _, ...safeUser } = user
    return safeUser
  }

  /**
   * 根据邮箱获取用户
   */
  async getUserByEmail(email: string): Promise<SafeUser | null> {
    const user = await this.userRepository.findByEmail(email)
    if (!user) {
      return null
    }
    
    const { password: _, ...safeUser } = user
    return safeUser
  }

  /**
   * 根据手机号获取用户
   */
  async getUserByPhone(phone: string): Promise<SafeUser | null> {
    const user = await this.userRepository.findByPhone(phone)
    if (!user) {
      return null
    }
    
    const { password: _, ...safeUser } = user
    return safeUser
  }

  /**
   * 创建用户
   */
  async createUser(userData: InsertUser): Promise<SafeUser> {
    // 如果提供了密码，需要加密
    if (userData.password) {
      userData.password = await this.authService.hashPassword(userData.password)
    }

    const user = await this.userRepository.create(userData)
    const { password: _, ...safeUser } = user
    return safeUser
  }

  /**
   * 更新用户信息
   */
  async updateUser(id: string, updates: Partial<InsertUser>): Promise<SafeUser | null> {
    // 如果更新密码，需要加密
    if (updates.password) {
      updates.password = await this.authService.hashPassword(updates.password)
    }

    const user = await this.userRepository.update(id, updates)
    if (!user) {
      return null
    }
    
    const { password: _, ...safeUser } = user
    return safeUser
  }

  /**
   * 更新用户密码
   */
  async updatePassword(id: string, oldPassword: string, newPassword: string): Promise<boolean> {
    const user = await this.userRepository.findById(id)
    if (!user) {
      throw new Error('USER_NOT_FOUND')
    }

    // 如果用户已有密码，需要验证旧密码
    if (user.password) {
      const isValidOldPassword = await this.authService.verifyPassword(oldPassword, user.password)
      if (!isValidOldPassword) {
        throw new Error('INVALID_OLD_PASSWORD')
      }
    }

    // 加密新密码
    const hashedNewPassword = await this.authService.hashPassword(newPassword)
    
    const updatedUser = await this.userRepository.update(id, { password: hashedNewPassword })
    return !!updatedUser
  }

  /**
   * 设置密码（用于首次设置密码）
   */
  async setPassword(id: string, password: string): Promise<boolean> {
    const hashedPassword = await this.authService.hashPassword(password)
    const updatedUser = await this.userRepository.update(id, { password: hashedPassword })
    return !!updatedUser
  }

  /**
   * 验证邮箱
   */
  async verifyEmail(id: string): Promise<boolean> {
    const updatedUser = await this.userRepository.update(id, { emailVerified: true })
    return !!updatedUser
  }

  /**
   * 验证手机号
   */
  async verifyPhone(id: string): Promise<boolean> {
    const updatedUser = await this.userRepository.update(id, { phoneVerified: true })
    return !!updatedUser
  }

  /**
   * 更新用户头像
   */
  async updateAvatar(id: string, avatarUrl: string): Promise<SafeUser | null> {
    const user = await this.userRepository.update(id, { avatar: avatarUrl })
    if (!user) {
      return null
    }
    
    const { password: _, ...safeUser } = user
    return safeUser
  }

  /**
   * 更新用户基本信息
   */
  async updateProfile(
    id: string,
    updates: Pick<InsertUser, 'name' | 'email' | 'phone'>
  ): Promise<SafeUser | null> {
    // 检查邮箱是否已被其他用户使用
    if (updates.email) {
      const existingUser = await this.userRepository.findByEmail(updates.email)
      if (existingUser && existingUser.id !== id) {
        throw new Error('EMAIL_ALREADY_EXISTS')
      }
    }

    // 检查手机号是否已被其他用户使用
    if (updates.phone) {
      const existingUser = await this.userRepository.findByPhone(updates.phone)
      if (existingUser && existingUser.id !== id) {
        throw new Error('PHONE_ALREADY_EXISTS')
      }
    }

    const user = await this.userRepository.update(id, updates)
    if (!user) {
      return null
    }
    
    const { password: _, ...safeUser } = user
    return safeUser
  }

  /**
   * 删除用户
   */
  async deleteUser(id: string): Promise<boolean> {
    return this.userRepository.delete(id)
  }

  /**
   * 检查用户是否存在
   */
  async userExists(id: string): Promise<boolean> {
    return this.userRepository.exists(id)
  }

  /**
   * 检查邮箱是否已存在
   */
  async emailExists(email: string): Promise<boolean> {
    const user = await this.userRepository.findByEmail(email)
    return !!user
  }

  /**
   * 检查手机号是否已存在
   */
  async phoneExists(phone: string): Promise<boolean> {
    const user = await this.userRepository.findByPhone(phone)
    return !!user
  }
}
