import type { Application, InsertApplication } from '@coozf/db/schema'
import type { ApplicationRepository } from '../../db/repositories/application.repository'
import type { CryptoService } from './crypto.service'

export class ApplicationService {
  constructor(
    private applicationRepository: ApplicationRepository,
    private cryptoService: CryptoService
  ) {}

  /**
   * 创建应用
   */
  async createApplication(
    userId: string,
    name: string,
    description?: string,
    webhookUrl?: string
  ): Promise<{ application: Application; secret: string }> {
    // 生成唯一的应用ID
    let appId: string
    let attempts = 0
    const maxAttempts = 10

    do {
      appId = this.cryptoService.generateAppId()
      attempts++
      if (attempts > maxAttempts) {
        throw new Error('APPID_GENERATION_FAILED')
      }
    } while (await this.applicationRepository.existsByAppId(appId))

    // 生成应用密钥
    const secret = this.cryptoService.generateAppSecret()
    const hashedSecret = await this.cryptoService.hashSecret(secret)

    // 生成 webhook 密钥
    const webhookSecret = this.cryptoService.generateSecret()

    const applicationData: InsertApplication = {
      userId,
      appId,
      name,
      description,
      secret: hashedSecret,
      webhookUrl,
      webhookSecret,
      status: 'active',
    }

    const application = await this.applicationRepository.create(applicationData)

    return { application, secret }
  }

  /**
   * 获取用户的应用列表
   */
  async getUserApplications(userId: string): Promise<Application[]> {
    return this.applicationRepository.findByUserId(userId)
  }

  /**
   * 根据应用ID获取应用
   */
  async getApplicationByAppId(appId: string): Promise<Application | null> {
    return this.applicationRepository.findByAppId(appId)
  }

  /**
   * 根据ID获取应用
   */
  async getApplicationById(id: string): Promise<Application | null> {
    return this.applicationRepository.findById(id)
  }

  /**
   * 验证应用密钥
   */
  async verifyApplicationSecret(appId: string, secret: string): Promise<Application | null> {
    const application = await this.applicationRepository.findByAppId(appId)
    if (!application) {
      return null
    }

    const isValidSecret = await this.cryptoService.verifySecret(secret, application.secret)
    if (!isValidSecret) {
      return null
    }

    return application
  }

  /**
   * 更新应用信息
   */
  async updateApplication(
    id: string,
    userId: string,
    updates: Partial<Pick<InsertApplication, 'name' | 'description' | 'webhookUrl' | 'status'>>
  ): Promise<Application | null> {
    // 验证应用是否属于该用户
    const application = await this.applicationRepository.findById(id)
    if (!application || application.userId !== userId) {
      throw new Error('APPLICATION_NOT_FOUND')
    }

    return this.applicationRepository.update(id, updates)
  }

  /**
   * 重新生成应用密钥
   */
  async regenerateSecret(id: string, userId: string): Promise<{ application: Application; secret: string }> {
    // 验证应用是否属于该用户
    const application = await this.applicationRepository.findById(id)
    if (!application || application.userId !== userId) {
      throw new Error('APPLICATION_NOT_FOUND')
    }

    // 生成新的密钥
    const newSecret = this.cryptoService.generateAppSecret()
    const hashedSecret = await this.cryptoService.hashSecret(newSecret)

    const updatedApplication = await this.applicationRepository.update(id, {
      secret: hashedSecret,
    })

    if (!updatedApplication) {
      throw new Error('SECRET_REGENERATION_FAILED')
    }

    return { application: updatedApplication, secret: newSecret }
  }

  /**
   * 删除应用
   */
  async deleteApplication(id: string, userId: string): Promise<boolean> {
    // 验证应用是否属于该用户
    const application = await this.applicationRepository.findById(id)
    if (!application || application.userId !== userId) {
      throw new Error('APPLICATION_NOT_FOUND')
    }

    return this.applicationRepository.delete(id)
  }

  /**
   * 检查应用是否存在
   */
  async applicationExists(id: string): Promise<boolean> {
    return this.applicationRepository.exists(id)
  }

  /**
   * 检查应用ID是否存在
   */
  async appIdExists(appId: string): Promise<boolean> {
    return this.applicationRepository.existsByAppId(appId)
  }
}
