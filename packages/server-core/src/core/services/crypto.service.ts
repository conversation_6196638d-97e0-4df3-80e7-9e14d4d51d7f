import bcrypt from 'bcryptjs'
import { randomBytes } from 'crypto'

const SALT_ROUNDS = 12

export class CryptoService {
  /**
   * 生成随机 Secret
   */
  generateSecret(): string {
    // 生成 32 位随机字符串，使用 base64 编码
    return randomBytes(24).toString('base64url') // base64url 避免特殊字符
  }

  /**
   * 哈希 Secret
   */
  async hashSecret(secret: string): Promise<string> {
    return bcrypt.hash(secret, SALT_ROUNDS)
  }

  /**
   * 验证 Secret
   */
  async verifySecret(secret: string, hashedSecret: string): Promise<boolean> {
    return bcrypt.compare(secret, hashedSecret)
  }

  /**
   * 格式化 Secret 用于显示（隐藏部分字符）
   */
  formatSecretForDisplay(secret: string): string {
    if (secret.length <= 8) {
      return secret
    }
    const start = secret.slice(0, 4)
    const end = secret.slice(-4)
    const middle = '*'.repeat(secret.length - 8)
    return `${start}${middle}${end}`
  }

  /**
   * 生成应用ID
   * 格式：app_ + 16位随机字符串
   */
  generateAppId(): string {
    const randomString = randomBytes(8).toString('hex') // 16位十六进制字符
    return `app_${randomString}`
  }

  /**
   * 生成应用密钥
   * 格式：sk_ + 32位随机字符串
   */
  generateAppSecret(): string {
    const randomString = randomBytes(16).toString('hex') // 32位十六进制字符
    return `sk_${randomString}`
  }

  /**
   * 生成 Session Token
   */
  generateSessionToken(): string {
    return `sess_${randomBytes(32).toString('hex')}`
  }

  /**
   * 生成订单号
   */
  generateOrderNo(): string {
    const timestamp = Date.now()
    const random = randomBytes(4).toString('hex').toUpperCase()
    return `ORD${timestamp}${random}`
  }
}
