import { describe, it, expect, beforeEach } from 'vitest'
import { CryptoService } from '../crypto.service'

describe('CryptoService', () => {
  let cryptoService: CryptoService

  beforeEach(() => {
    cryptoService = new CryptoService()
  })

  describe('generateSecret', () => {
    it('should generate a random secret', () => {
      const secret1 = cryptoService.generateSecret()
      const secret2 = cryptoService.generateSecret()
      
      expect(secret1).toBeDefined()
      expect(secret2).toBeDefined()
      expect(secret1).not.toBe(secret2)
      expect(secret1.length).toBeGreaterThan(0)
    })
  })

  describe('hashSecret and verifySecret', () => {
    it('should hash and verify secret correctly', async () => {
      const originalSecret = 'test-secret-123'
      const hashedSecret = await cryptoService.hashSecret(originalSecret)
      
      expect(hashedSecret).toBeDefined()
      expect(hashedSecret).not.toBe(originalSecret)
      
      const isValid = await cryptoService.verifySecret(originalSecret, hashedSecret)
      expect(isValid).toBe(true)
      
      const isInvalid = await cryptoService.verifySecret('wrong-secret', hashedSecret)
      expect(isInvalid).toBe(false)
    })
  })

  describe('formatSecretForDisplay', () => {
    it('should format long secrets correctly', () => {
      const longSecret = 'abcdefghijklmnopqrstuvwxyz'
      const formatted = cryptoService.formatSecretForDisplay(longSecret)
      
      expect(formatted).toBe('abcd******************wxyz')
    })

    it('should return short secrets as-is', () => {
      const shortSecret = 'abc'
      const formatted = cryptoService.formatSecretForDisplay(shortSecret)
      
      expect(formatted).toBe('abc')
    })

    it('should handle exactly 8 character secrets', () => {
      const eightCharSecret = 'abcdefgh'
      const formatted = cryptoService.formatSecretForDisplay(eightCharSecret)
      
      expect(formatted).toBe('abcdefgh')
    })
  })

  describe('generateAppId', () => {
    it('should generate app ID with correct format', () => {
      const appId = cryptoService.generateAppId()
      
      expect(appId).toMatch(/^app_[a-f0-9]{16}$/)
    })

    it('should generate unique app IDs', () => {
      const appId1 = cryptoService.generateAppId()
      const appId2 = cryptoService.generateAppId()
      
      expect(appId1).not.toBe(appId2)
    })
  })

  describe('generateAppSecret', () => {
    it('should generate app secret with correct format', () => {
      const appSecret = cryptoService.generateAppSecret()
      
      expect(appSecret).toMatch(/^sk_[a-f0-9]{32}$/)
    })

    it('should generate unique app secrets', () => {
      const secret1 = cryptoService.generateAppSecret()
      const secret2 = cryptoService.generateAppSecret()
      
      expect(secret1).not.toBe(secret2)
    })
  })

  describe('generateSessionToken', () => {
    it('should generate session token with correct format', () => {
      const sessionToken = cryptoService.generateSessionToken()
      
      expect(sessionToken).toMatch(/^sess_[a-f0-9]{64}$/)
    })

    it('should generate unique session tokens', () => {
      const token1 = cryptoService.generateSessionToken()
      const token2 = cryptoService.generateSessionToken()
      
      expect(token1).not.toBe(token2)
    })
  })

  describe('generateOrderNo', () => {
    it('should generate order number with correct format', () => {
      const orderNo = cryptoService.generateOrderNo()
      
      expect(orderNo).toMatch(/^ORD\d{13}[A-F0-9]{8}$/)
    })

    it('should generate unique order numbers', () => {
      const orderNo1 = cryptoService.generateOrderNo()
      const orderNo2 = cryptoService.generateOrderNo()
      
      expect(orderNo1).not.toBe(orderNo2)
    })
  })
})
