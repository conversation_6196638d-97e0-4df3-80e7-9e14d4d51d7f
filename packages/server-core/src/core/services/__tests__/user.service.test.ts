import { describe, it, expect, beforeEach, vi } from 'vitest'
import { UserService } from '../user.service'
import { AuthService } from '../auth.service'
import type { UserRepository } from '../../../db/repositories/user.repository'
import type { User, InsertUser } from '@coozf/db/schema'

// Mock Repository
const createMockUserRepository = (): UserRepository => ({
  findById: vi.fn(),
  findByEmail: vi.fn(),
  findByPhone: vi.fn(),
  create: vi.fn(),
  update: vi.fn(),
  delete: vi.fn(),
  exists: vi.fn(),
} as any)

// Mock AuthService
const createMockAuthService = (): AuthService => ({
  hashPassword: vi.fn(),
  verifyPassword: vi.fn(),
} as any)

describe('UserService', () => {
  let userService: UserService
  let mockUserRepository: UserRepository
  let mockAuthService: AuthService

  const mockUser: User = {
    id: 'user-123',
    name: 'Test User',
    email: '<EMAIL>',
    phone: '13800138000',
    password: 'hashed-password',
    avatar: 'https://example.com/avatar.jpg',
    emailVerified: false,
    phoneVerified: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  }

  beforeEach(() => {
    mockUserRepository = createMockUserRepository()
    mockAuthService = createMockAuthService()
    userService = new UserService(mockUserRepository, mockAuthService)
  })

  describe('getUserById', () => {
    it('should return user without password', async () => {
      vi.mocked(mockUserRepository.findById).mockResolvedValue(mockUser)

      const result = await userService.getUserById('user-123')

      expect(result).toBeDefined()
      expect(result?.id).toBe(mockUser.id)
      expect(result?.name).toBe(mockUser.name)
      expect('password' in result!).toBe(false)
      expect(mockUserRepository.findById).toHaveBeenCalledWith('user-123')
    })

    it('should return null if user not found', async () => {
      vi.mocked(mockUserRepository.findById).mockResolvedValue(null)

      const result = await userService.getUserById('non-existent')

      expect(result).toBeNull()
      expect(mockUserRepository.findById).toHaveBeenCalledWith('non-existent')
    })
  })

  describe('getUserByEmail', () => {
    it('should return user without password', async () => {
      vi.mocked(mockUserRepository.findByEmail).mockResolvedValue(mockUser)

      const result = await userService.getUserByEmail('<EMAIL>')

      expect(result).toBeDefined()
      expect(result?.email).toBe(mockUser.email)
      expect('password' in result!).toBe(false)
      expect(mockUserRepository.findByEmail).toHaveBeenCalledWith('<EMAIL>')
    })
  })

  describe('createUser', () => {
    it('should create user and hash password if provided', async () => {
      const userData: InsertUser = {
        name: 'New User',
        email: '<EMAIL>',
        password: 'plain-password',
      }

      const hashedPassword = 'hashed-password'
      vi.mocked(mockAuthService.hashPassword).mockResolvedValue(hashedPassword)
      vi.mocked(mockUserRepository.create).mockResolvedValue({
        ...mockUser,
        ...userData,
        password: hashedPassword,
      })

      const result = await userService.createUser(userData)

      expect(mockAuthService.hashPassword).toHaveBeenCalledWith('plain-password')
      expect(mockUserRepository.create).toHaveBeenCalledWith({
        ...userData,
        password: hashedPassword,
      })
      expect(result).toBeDefined()
      expect('password' in result).toBe(false)
    })

    it('should create user without hashing if no password provided', async () => {
      const userData: InsertUser = {
        name: 'New User',
        email: '<EMAIL>',
      }

      vi.mocked(mockUserRepository.create).mockResolvedValue({
        ...mockUser,
        ...userData,
        password: null,
      })

      const result = await userService.createUser(userData)

      expect(mockAuthService.hashPassword).not.toHaveBeenCalled()
      expect(mockUserRepository.create).toHaveBeenCalledWith(userData)
      expect(result).toBeDefined()
    })
  })

  describe('updatePassword', () => {
    it('should update password after verifying old password', async () => {
      const oldPassword = 'old-password'
      const newPassword = 'new-password'
      const hashedNewPassword = 'hashed-new-password'

      vi.mocked(mockUserRepository.findById).mockResolvedValue(mockUser)
      vi.mocked(mockAuthService.verifyPassword).mockResolvedValue(true)
      vi.mocked(mockAuthService.hashPassword).mockResolvedValue(hashedNewPassword)
      vi.mocked(mockUserRepository.update).mockResolvedValue({
        ...mockUser,
        password: hashedNewPassword,
      })

      const result = await userService.updatePassword('user-123', oldPassword, newPassword)

      expect(mockAuthService.verifyPassword).toHaveBeenCalledWith(oldPassword, mockUser.password)
      expect(mockAuthService.hashPassword).toHaveBeenCalledWith(newPassword)
      expect(mockUserRepository.update).toHaveBeenCalledWith('user-123', {
        password: hashedNewPassword,
      })
      expect(result).toBe(true)
    })

    it('should throw error if user not found', async () => {
      vi.mocked(mockUserRepository.findById).mockResolvedValue(null)

      await expect(
        userService.updatePassword('non-existent', 'old', 'new')
      ).rejects.toThrow('USER_NOT_FOUND')
    })

    it('should throw error if old password is invalid', async () => {
      vi.mocked(mockUserRepository.findById).mockResolvedValue(mockUser)
      vi.mocked(mockAuthService.verifyPassword).mockResolvedValue(false)

      await expect(
        userService.updatePassword('user-123', 'wrong-password', 'new-password')
      ).rejects.toThrow('INVALID_OLD_PASSWORD')
    })

    it('should allow setting password if user has no existing password', async () => {
      const userWithoutPassword = { ...mockUser, password: null }
      const newPassword = 'new-password'
      const hashedNewPassword = 'hashed-new-password'

      vi.mocked(mockUserRepository.findById).mockResolvedValue(userWithoutPassword)
      vi.mocked(mockAuthService.hashPassword).mockResolvedValue(hashedNewPassword)
      vi.mocked(mockUserRepository.update).mockResolvedValue({
        ...userWithoutPassword,
        password: hashedNewPassword,
      })

      const result = await userService.updatePassword('user-123', '', newPassword)

      expect(mockAuthService.verifyPassword).not.toHaveBeenCalled()
      expect(mockAuthService.hashPassword).toHaveBeenCalledWith(newPassword)
      expect(result).toBe(true)
    })
  })

  describe('updateProfile', () => {
    it('should update user profile', async () => {
      const updates = {
        name: 'Updated Name',
        email: '<EMAIL>',
      }

      vi.mocked(mockUserRepository.findByEmail).mockResolvedValue(null)
      vi.mocked(mockUserRepository.update).mockResolvedValue({
        ...mockUser,
        ...updates,
      })

      const result = await userService.updateProfile('user-123', updates)

      expect(mockUserRepository.update).toHaveBeenCalledWith('user-123', updates)
      expect(result?.name).toBe(updates.name)
      expect(result?.email).toBe(updates.email)
    })

    it('should throw error if email already exists for another user', async () => {
      const updates = { email: '<EMAIL>' }
      const existingUser = { ...mockUser, id: 'other-user', email: '<EMAIL>' }

      vi.mocked(mockUserRepository.findByEmail).mockResolvedValue(existingUser)

      await expect(
        userService.updateProfile('user-123', updates)
      ).rejects.toThrow('EMAIL_ALREADY_EXISTS')
    })

    it('should allow updating to same email for same user', async () => {
      const updates = { email: mockUser.email }

      vi.mocked(mockUserRepository.findByEmail).mockResolvedValue(mockUser)
      vi.mocked(mockUserRepository.update).mockResolvedValue(mockUser)

      const result = await userService.updateProfile('user-123', updates)

      expect(result).toBeDefined()
      expect(mockUserRepository.update).toHaveBeenCalled()
    })
  })
})
