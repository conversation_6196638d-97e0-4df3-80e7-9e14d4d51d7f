import { describe, it, expect, beforeEach } from 'vitest'
import { JwtService } from '../jwt.service'

describe('JwtService', () => {
  let jwtService: JwtService
  const testSecret = 'test-jwt-secret-key'

  beforeEach(() => {
    jwtService = new JwtService(testSecret)
  })

  describe('generateToken and verifyToken', () => {
    it('should generate and verify JWT token correctly', () => {
      const payload = {
        userId: 'user-123',
        phone: '13800138000',
      }

      const token = jwtService.generateToken(payload)
      expect(token).toBeDefined()
      expect(typeof token).toBe('string')

      const verifiedPayload = jwtService.verifyToken(token)
      expect(verifiedPayload.userId).toBe(payload.userId)
      expect(verifiedPayload.phone).toBe(payload.phone)
    })

    it('should throw error for invalid token', () => {
      expect(() => {
        jwtService.verifyToken('invalid-token')
      }).toThrow('TOKEN_INVALID')
    })

    it('should throw error for token with wrong secret', () => {
      const anotherJwtService = new JwtService('different-secret')
      const payload = { userId: 'user-123', phone: '13800138000' }
      
      const token = anotherJwtService.generateToken(payload)
      
      expect(() => {
        jwtService.verifyToken(token)
      }).toThrow('TOKEN_INVALID')
    })
  })

  describe('generateOpenAPIToken and verifyOpenAPIToken', () => {
    it('should generate and verify OpenAPI token correctly', () => {
      const payload = {
        userId: 'user-123',
        appId: 'app_123456789abcdef0',
        type: 'open_api' as const,
      }

      const token = jwtService.generateOpenAPIToken(payload)
      expect(token).toBeDefined()
      expect(typeof token).toBe('string')

      const verifiedPayload = jwtService.verifyOpenAPIToken(token)
      expect(verifiedPayload.userId).toBe(payload.userId)
      expect(verifiedPayload.appId).toBe(payload.appId)
      expect(verifiedPayload.type).toBe(payload.type)
    })

    it('should throw error for invalid OpenAPI token', () => {
      expect(() => {
        jwtService.verifyOpenAPIToken('invalid-token')
      }).toThrow('TOKEN_INVALID')
    })

    it('should throw error for regular token used as OpenAPI token', () => {
      const regularPayload = { userId: 'user-123', phone: '13800138000' }
      const regularToken = jwtService.generateToken(regularPayload)
      
      expect(() => {
        jwtService.verifyOpenAPIToken(regularToken)
      }).toThrow('TOKEN_INVALID')
    })

    it('should throw error for OpenAPI token without correct type', () => {
      // This would be a malformed token, but let's test the type checking
      const payload = {
        userId: 'user-123',
        appId: 'app_123456789abcdef0',
        type: 'wrong_type' as any,
      }

      const token = jwtService.generateOpenAPIToken(payload)
      
      expect(() => {
        jwtService.verifyOpenAPIToken(token)
      }).toThrow('TOKEN_INVALID')
    })
  })

  describe('decodeToken', () => {
    it('should decode token without verification', () => {
      const payload = {
        userId: 'user-123',
        phone: '13800138000',
      }

      const token = jwtService.generateToken(payload)
      const decoded = jwtService.decodeToken(token)
      
      expect(decoded).toBeDefined()
      expect(decoded.userId).toBe(payload.userId)
      expect(decoded.phone).toBe(payload.phone)
      expect(decoded.exp).toBeDefined() // Should have expiration
      expect(decoded.iat).toBeDefined() // Should have issued at
    })

    it('should decode invalid token structure without throwing', () => {
      // decodeToken should not throw for malformed tokens, just return null or the decoded structure
      const result = jwtService.decodeToken('invalid.token.structure')
      expect(result).toBeNull()
    })
  })
})
