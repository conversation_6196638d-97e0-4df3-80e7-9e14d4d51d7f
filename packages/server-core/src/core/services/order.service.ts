import type { Order, InsertOrder } from '@coozf/db/schema'
import type { OrderRepository } from '../../db/repositories/order.repository'
import type { BalanceService } from './balance.service'
import type { CryptoService } from './crypto.service'

export interface CreateOrderParams {
  userId: string
  applicationId: string
  amount: number
  type?: 'GIFT' | 'PURCHASE'
  remarks?: string
}

export interface OrderListParams {
  page?: number
  pageSize?: number
  userId?: string
  applicationId?: string
  status?: string
  type?: string
  startDate?: string
  endDate?: string
  search?: string
}

export class OrderService {
  constructor(
    private orderRepository: OrderRepository,
    private balanceService: BalanceService,
    private cryptoService: CryptoService
  ) {}

  /**
   * 创建充值订单并直接完成充值
   */
  async createRechargeOrder(params: CreateOrderParams): Promise<string> {
    const { userId, applicationId, amount, type = 'GIFT', remarks } = params
    
    const orderNo = this.cryptoService.generateOrderNo()
    const antCoins = amount // 1元 = 1蚁贝

    const orderData: InsertOrder = {
      orderNo,
      userId,
      applicationId,
      antCoins: antCoins.toFixed(2),
      amount: amount.toFixed(2),
      type,
      status: 'COMPLETED', // 直接完成
      remarks,
    }

    // 创建订单
    const order = await this.orderRepository.create(orderData)

    // 直接充值到应用余额
    await this.balanceService.rechargeBalance(applicationId, antCoins)

    return orderNo
  }

  /**
   * 获取订单列表
   */
  async getOrderList(params: OrderListParams) {
    const { page = 1, pageSize = 10, userId, applicationId } = params

    let orders: Order[]
    
    if (userId && applicationId) {
      orders = await this.orderRepository.findByUserIdAndAppId(userId, applicationId, pageSize)
    } else if (userId) {
      orders = await this.orderRepository.findByUserId(userId, pageSize)
    } else if (applicationId) {
      orders = await this.orderRepository.findByApplicationId(applicationId, pageSize)
    } else {
      // 如果没有指定用户或应用，返回空列表
      orders = []
    }

    // 简单的分页处理（实际项目中可能需要更复杂的分页逻辑）
    const startIndex = (page - 1) * pageSize
    const paginatedOrders = orders.slice(startIndex, startIndex + pageSize)

    return {
      data: paginatedOrders,
      total: orders.length,
      page,
      pageSize,
    }
  }

  /**
   * 获取订单详情
   */
  async getOrderById(orderId: string): Promise<Order | null> {
    return this.orderRepository.findById(orderId)
  }

  /**
   * 根据订单号获取订单
   */
  async getOrderByOrderNo(orderNo: string): Promise<Order | null> {
    return this.orderRepository.findByOrderNo(orderNo)
  }

  /**
   * 更新订单状态
   */
  async updateOrderStatus(
    orderId: string,
    status: string,
    remarks?: string
  ): Promise<Order | null> {
    const updateData: Partial<InsertOrder> = { status }
    if (remarks) {
      updateData.remarks = remarks
    }

    return this.orderRepository.update(orderId, updateData)
  }

  /**
   * 取消订单
   */
  async cancelOrder(orderId: string): Promise<Order | null> {
    const order = await this.orderRepository.findById(orderId)
    if (!order) {
      throw new Error('ORDER_NOT_FOUND')
    }

    if (order.status === 'COMPLETED') {
      throw new Error('CANNOT_CANCEL_COMPLETED_ORDER')
    }

    return this.updateOrderStatus(orderId, 'CANCELLED', '订单已取消')
  }

  /**
   * 申请发票
   */
  async requestInvoice(orderId: string): Promise<Order | null> {
    return this.orderRepository.update(orderId, { invoiceRequested: true })
  }

  /**
   * 获取用户订单统计
   */
  async getUserOrderStats(userId: string): Promise<{
    totalOrders: number
    completedOrders: number
    totalAmount: number
    totalAntCoins: number
  }> {
    const orders = await this.orderRepository.findByUserId(userId)
    
    const totalOrders = orders.length
    const completedOrders = orders.filter(order => order.status === 'COMPLETED').length
    const totalAmount = orders
      .filter(order => order.status === 'COMPLETED')
      .reduce((sum, order) => sum + parseFloat(order.amount), 0)
    const totalAntCoins = orders
      .filter(order => order.status === 'COMPLETED')
      .reduce((sum, order) => sum + parseFloat(order.antCoins), 0)

    return {
      totalOrders,
      completedOrders,
      totalAmount,
      totalAntCoins,
    }
  }

  /**
   * 获取应用订单统计
   */
  async getApplicationOrderStats(applicationId: string): Promise<{
    totalOrders: number
    completedOrders: number
    totalAmount: number
    totalAntCoins: number
  }> {
    const orders = await this.orderRepository.findByApplicationId(applicationId)
    
    const totalOrders = orders.length
    const completedOrders = orders.filter(order => order.status === 'COMPLETED').length
    const totalAmount = orders
      .filter(order => order.status === 'COMPLETED')
      .reduce((sum, order) => sum + parseFloat(order.amount), 0)
    const totalAntCoins = orders
      .filter(order => order.status === 'COMPLETED')
      .reduce((sum, order) => sum + parseFloat(order.antCoins), 0)

    return {
      totalOrders,
      completedOrders,
      totalAmount,
      totalAntCoins,
    }
  }

  /**
   * 检查订单是否存在
   */
  async orderExists(orderId: string): Promise<boolean> {
    return this.orderRepository.exists(orderId)
  }

  /**
   * 检查订单号是否存在
   */
  async orderNoExists(orderNo: string): Promise<boolean> {
    return this.orderRepository.existsByOrderNo(orderNo)
  }
}
