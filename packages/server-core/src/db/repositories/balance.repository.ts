import { eq, and } from 'drizzle-orm'
import { applicationBalances, type ApplicationBalance, type InsertApplicationBalance } from '@coozf/db/schema'
import type { DatabaseClient } from '../client'

export class BalanceRepository {
  constructor(private db: DatabaseClient) {}

  async findById(id: string): Promise<ApplicationBalance | null> {
    const result = await this.db.query.applicationBalances.findFirst({
      where: eq(applicationBalances.id, id),
    })
    return result || null
  }

  async findByApplicationId(applicationId: string): Promise<ApplicationBalance | null> {
    const result = await this.db.query.applicationBalances.findFirst({
      where: eq(applicationBalances.applicationId, applicationId),
    })
    return result || null
  }

  async findByApplicationIds(applicationIds: string[]): Promise<ApplicationBalance[]> {
    if (applicationIds.length === 0) return []

    const result = await this.db.query.applicationBalances.findMany({
      where: (table, { inArray }) => inArray(table.applicationId, applicationIds),
    })
    return result
  }

  async create(balanceData: InsertApplicationBalance): Promise<ApplicationBalance> {
    const [result] = await this.db
      .insert(applicationBalances)
      .values(balanceData)
      .returning()

    if (!result) {
      throw new Error('Failed to create balance')
    }

    return result
  }

  async update(id: string, balanceData: Partial<InsertApplicationBalance>): Promise<ApplicationBalance | null> {
    const [result] = await this.db
      .update(applicationBalances)
      .set(balanceData)
      .where(eq(applicationBalances.id, id))
      .returning()

    return result || null
  }

  async updateBalance(applicationId: string, newBalance: string): Promise<ApplicationBalance | null> {
    const [result] = await this.db
      .update(applicationBalances)
      .set({ balance: newBalance })
      .where(eq(applicationBalances.applicationId, applicationId))
      .returning()

    return result || null
  }

  async delete(id: string): Promise<boolean> {
    const result = await this.db
      .delete(applicationBalances)
      .where(eq(applicationBalances.id, id))

    return (result.rowCount ?? 0) > 0
  }

  async exists(applicationId: string): Promise<boolean> {
    const result = await this.db.query.applicationBalances.findFirst({
      where: eq(applicationBalances.applicationId, applicationId),
      columns: { id: true },
    })
    return !!result
  }
}
