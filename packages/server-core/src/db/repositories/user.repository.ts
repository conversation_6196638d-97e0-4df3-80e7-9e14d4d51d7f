import { eq } from 'drizzle-orm'
import { users, type User, type InsertUser } from '@coozf/db/schema'
import type { DatabaseClient } from '../client'

export class UserRepository {
  constructor(private db: DatabaseClient) {}

  async findById(id: string): Promise<User | null> {
    const result = await this.db.query.users.findFirst({
      where: eq(users.id, id),
    })
    return result || null
  }

  async findByEmail(email: string): Promise<User | null> {
    const result = await this.db.query.users.findFirst({
      where: eq(users.email, email),
    })
    return result || null
  }

  async findByPhone(phone: string): Promise<User | null> {
    const result = await this.db.query.users.findFirst({
      where: eq(users.phone, phone),
    })
    return result || null
  }

  async create(userData: InsertUser): Promise<User> {
    const [result] = await this.db
      .insert(users)
      .values(userData)
      .returning()
    
    if (!result) {
      throw new Error('Failed to create user')
    }
    
    return result
  }

  async update(id: string, userData: Partial<InsertUser>): Promise<User | null> {
    const [result] = await this.db
      .update(users)
      .set(userData)
      .where(eq(users.id, id))
      .returning()
    
    return result || null
  }

  async delete(id: string): Promise<boolean> {
    const result = await this.db
      .delete(users)
      .where(eq(users.id, id))

    return (result.rowCount ?? 0) > 0
  }

  async exists(id: string): Promise<boolean> {
    const result = await this.db.query.users.findFirst({
      where: eq(users.id, id),
      columns: { id: true },
    })
    return !!result
  }
}
