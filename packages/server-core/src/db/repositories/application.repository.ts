import { eq, and } from 'drizzle-orm'
import { applications, type Application, type InsertApplication } from '@coozf/db/schema'
import type { DatabaseClient } from '../client'

export class ApplicationRepository {
  constructor(private db: DatabaseClient) {}

  async findById(id: string): Promise<Application | null> {
    const result = await this.db.query.applications.findFirst({
      where: eq(applications.id, id),
    })
    return result || null
  }

  async findByAppId(appId: string): Promise<Application | null> {
    const result = await this.db.query.applications.findFirst({
      where: eq(applications.appId, appId),
    })
    return result || null
  }

  async findByUserId(userId: string): Promise<Application[]> {
    const result = await this.db.query.applications.findMany({
      where: eq(applications.userId, userId),
    })
    return result
  }

  async findByUserIdAndAppId(userId: string, appId: string): Promise<Application | null> {
    const result = await this.db.query.applications.findFirst({
      where: and(
        eq(applications.userId, userId),
        eq(applications.appId, appId)
      ),
    })
    return result || null
  }

  async create(applicationData: InsertApplication): Promise<Application> {
    const [result] = await this.db
      .insert(applications)
      .values(applicationData)
      .returning()
    
    if (!result) {
      throw new Error('Failed to create application')
    }
    
    return result
  }

  async update(id: string, applicationData: Partial<InsertApplication>): Promise<Application | null> {
    const [result] = await this.db
      .update(applications)
      .set(applicationData)
      .where(eq(applications.id, id))
      .returning()
    
    return result || null
  }

  async delete(id: string): Promise<boolean> {
    const result = await this.db
      .delete(applications)
      .where(eq(applications.id, id))

    return (result.rowCount ?? 0) > 0
  }

  async exists(id: string): Promise<boolean> {
    const result = await this.db.query.applications.findFirst({
      where: eq(applications.id, id),
      columns: { id: true },
    })
    return !!result
  }

  async existsByAppId(appId: string): Promise<boolean> {
    const result = await this.db.query.applications.findFirst({
      where: eq(applications.appId, appId),
      columns: { id: true },
    })
    return !!result
  }
}
