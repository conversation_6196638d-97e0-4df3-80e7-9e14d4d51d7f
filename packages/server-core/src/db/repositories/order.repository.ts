import { eq, and, desc } from 'drizzle-orm'
import { orders, type Order, type InsertOrder } from '@coozf/db/schema'
import type { DatabaseClient } from '../client'

export class OrderRepository {
  constructor(private db: DatabaseClient) {}

  async findById(id: string): Promise<Order | null> {
    const result = await this.db.query.orders.findFirst({
      where: eq(orders.id, id),
    })
    return result || null
  }

  async findByOrderNo(orderNo: string): Promise<Order | null> {
    const result = await this.db.query.orders.findFirst({
      where: eq(orders.orderNo, orderNo),
    })
    return result || null
  }

  async findByUserId(userId: string, limit?: number): Promise<Order[]> {
    const result = await this.db.query.orders.findMany({
      where: eq(orders.userId, userId),
      orderBy: [desc(orders.createdAt)],
      limit: limit,
    })
    return result
  }

  async findByUserIdAndAppId(userId: string, applicationId: string, limit?: number): Promise<Order[]> {
    const result = await this.db.query.orders.findMany({
      where: and(
        eq(orders.userId, userId),
        eq(orders.applicationId, applicationId)
      ),
      orderBy: [desc(orders.createdAt)],
      limit: limit,
    })
    return result
  }

  async findByApplicationId(applicationId: string, limit?: number): Promise<Order[]> {
    const result = await this.db.query.orders.findMany({
      where: eq(orders.applicationId, applicationId),
      orderBy: [desc(orders.createdAt)],
      limit: limit,
    })
    return result
  }

  async create(orderData: InsertOrder): Promise<Order> {
    const [result] = await this.db
      .insert(orders)
      .values(orderData)
      .returning()
    
    if (!result) {
      throw new Error('Failed to create order')
    }
    
    return result
  }

  async update(id: string, orderData: Partial<InsertOrder>): Promise<Order | null> {
    const [result] = await this.db
      .update(orders)
      .set(orderData)
      .where(eq(orders.id, id))
      .returning()
    
    return result || null
  }

  async updateStatus(id: string, status: string): Promise<Order | null> {
    const [result] = await this.db
      .update(orders)
      .set({ status })
      .where(eq(orders.id, id))
      .returning()
    
    return result || null
  }

  async delete(id: string): Promise<boolean> {
    const result = await this.db
      .delete(orders)
      .where(eq(orders.id, id))

    return (result.rowCount ?? 0) > 0
  }

  async exists(id: string): Promise<boolean> {
    const result = await this.db.query.orders.findFirst({
      where: eq(orders.id, id),
      columns: { id: true },
    })
    return !!result
  }

  async existsByOrderNo(orderNo: string): Promise<boolean> {
    const result = await this.db.query.orders.findFirst({
      where: eq(orders.orderNo, orderNo),
      columns: { id: true },
    })
    return !!result
  }
}
