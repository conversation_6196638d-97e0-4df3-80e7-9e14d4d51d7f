import './env'
import type { FastifyTRPCPluginOptions, CreateFastifyContextOptions } from '@trpc/server/adapters/fastify'
import { fastifyTRPCPlugin } from '@trpc/server/adapters/fastify'
import Fastify from 'fastify'
import fastifyCookie from '@fastify/cookie'
import { openApiDocument } from './openapi'
import { fastifyTRPCOpenApiPlugin } from 'trpc-to-openapi'
import { config, isdev } from './env'
import fastifyStatic from '@fastify/static'
import path from 'path'

// 导入新的 server-core 包
import { appRouter, openApiRouter, createContainer } from '@my-org/server-core'
import type { AppRouter, Context, ContainerConfig } from '@my-org/server-core'

// 创建容器实例
const containerConfig: ContainerConfig = {
  jwtSecret: config.JWT_SECRET,
  redis: {
    host: config.redis.host,
    port: config.redis.port,
    password: config.redis.password,
    username: config.redis.username,
    db: config.redis.db,
  },
}

const container = createContainer(containerConfig)

// 创建 Context
const createContext = async (opts: CreateFastifyContextOptions): Promise<Context> => {
  return {
    req: opts.req,
    res: opts.res,
    user: null,
    isPublicApi: false,
    container,
  }
}

const createPublicContext = async (opts: CreateFastifyContextOptions): Promise<Context> => {
  const context = await createContext(opts)
  return { ...context, isPublicApi: true }
}

// export const mergeRouters = t.mergeRouters

const app = Fastify({
  logger: {
    level: isdev ? 'info' : 'warn',
    serializers: {
      req: (req) => ({
        method: req.method,
        url: req.url.split('?')[0], // 移除查询参数
      }),
      res: (res) => ({
        statusCode: res.statusCode,
      }),
    },
    transport: isdev
      ? {
          target: 'pino-pretty',
          options: {
            translateTime: 'HH:MM:ss',
            ignore: 'pid,hostname,reqId,responseTime',
            colorize: true,
            levelFirst: false,
            singleLine: true,
            messageFormat: '{msg} {req.method} {req.url} {res.statusCode}',
          },
        }
      : undefined,
  },
})

async function main() {
  // 健康检查容器
  const healthCheck = await container.healthCheck()
  if (!healthCheck.redis || !healthCheck.database) {
    app.log.error('Container health check failed:', healthCheck)
    throw new Error('Container initialization failed')
  }
  app.log.info('✅ Container health check passed')

  await app.register(fastifyCookie)

  await app.register(fastifyStatic, {
    root: path.join(import.meta.dirname, '../public'),
    prefix: '/',
  })

  // SPA路由支持
  app.setNotFoundHandler(async (request, reply) => {
    if (request.url.startsWith('/api/')) {
      return reply.status(404).send({ error: 'API route not found' })
    }
    return reply.sendFile('index.html')
  })

  await app.register(fastifyTRPCPlugin, {
    prefix: '/api/trpc',
    useWSS: false,
    trpcOptions: {
      router: appRouter,
      createContext,
      onError({ path, error }) {
        // report to error monitoring
        console.error(`Error in tRPC handler on path '${path}':`, error)
      },
    } satisfies FastifyTRPCPluginOptions<AppRouter>['trpcOptions'],
  })

  await app.register(fastifyTRPCOpenApiPlugin, {
    basePath: '/api/open',
    router: openApiRouter,
    createContext: createPublicContext,
  })

  app.get('/openapi.json', () => openApiDocument)

  // 注册路由已通过 tRPC 插件完成

  app.get('/panel', async (_, res) => {
    if (!isdev) {
      return res.status(404).send('Not Found')
    }

    // Dynamically import renderTrpcPanel only in dev
    const { renderTrpcPanel } = await import('trpc-ui')

    return res
      .status(200)
      .header('Content-Type', 'text/html')
      .send(
        renderTrpcPanel(appRouter, {
          url: '/api/trpc',
          transformer: 'superjson',
          meta: {
            title: 'trpc-server',
            description: 'trpc-server',
          },
        }),
      )
  })

  app.setErrorHandler((error, _, reply) => {
    app.log.error(error)
    reply.status(500).send({ error: 'Internal Server Error' })
  })

  const port = Number(config.PORT) || 2022
  await app.listen({
    port,
    host: '0.0.0.0',
  })

  app.log.info(`🚀 服务器启动成功！`)
  app.log.info(`📡 API 地址: http://localhost:${port}/trpc`)
  app.log.info(`🎛️  控制面板: http://localhost:${port}/panel`)
  app.log.info(`🌍 环境: ${config.NODE_ENV}`)
}

// 优雅关闭处理
const gracefulShutdown = async (signal: string) => {
  app.log.info(`收到 ${signal} 信号，开始优雅关闭...`)

  try {
    // 关闭 Fastify 服务器
    await app.close()
    app.log.info('✅ Fastify 服务器已关闭')

    // 关闭容器资源
    await container.close()
    app.log.info('✅ 容器资源已清理')

    process.exit(0)
  } catch (error) {
    app.log.error(error, '❌ 优雅关闭失败')
    process.exit(1)
  }
}

// 监听进程信号
process.on('SIGTERM', () => void gracefulShutdown('SIGTERM'))
process.on('SIGINT', () => void gracefulShutdown('SIGINT'))

main().catch((err) => {
  app.log.error(err, '❌ 服务器启动失败')
  console.error(err)
  process.exit(1)
})
