// import { generateOpenApiDocument } from 'trpc-to-openapi'
// import { extendZodWithOpenApi } from 'zod-openapi'
// import { z } from 'zod'
// import { openApiRouter } from '@my-org/server-core'

// extendZodWithOpenApi(z)

// Generate OpenAPI schema document
export const openApiDocument = {
  openapi: '3.0.0',
  info: {
    title: 'tRPC OpenAPI',
    version: '1.0.0',
  },
  paths: {},
}

// TODO: Fix OpenAPI generation
// export const openApiDocument = generateOpenApiDocument(openApiRouter, {
//   title: 'tRPC OpenAPI',
//   version: '1.0.0',
//   baseUrl: 'http://localhost:3000/api/open',
// })
