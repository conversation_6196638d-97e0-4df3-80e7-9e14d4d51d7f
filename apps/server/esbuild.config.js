import { build } from 'esbuild'
import { readFileSync } from 'fs'
// 读取 package.json 来自动获取依赖列表
const pkg = JSON.parse(readFileSync('./package.json', 'utf8'))
const dependencies = Object.keys(pkg.dependencies || {})

await build({
  entryPoints: ['src/index.ts'],
  bundle: true,
  platform: 'node',
  target: 'node18',
  format: 'esm',
  outfile: 'dist/index.js',
  sourcemap: true,
  external: [
    ...dependencies,
    // Node.js 内置模块
    'fs',
    'path',
    'crypto',
    'os',
    'util',
    'events',
    'stream',
    'process',
    'http',
    'https',
    'url',
    'querystring',
    'buffer',
    'child_process',
  ],
  // 处理 tsconfig paths
  alias: {
    '@': './src',
  },
})
