import { createFileRoute } from '@tanstack/react-router'
import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { trpc } from '@/lib/trpc'
import { ShoppingCart, Receipt, Filter } from 'lucide-react'
import { toast } from 'sonner'

export const Route = createFileRoute('/_authenticated/orders')({
  component: OrdersPage,
})

function OrdersPage() {
  const [page, setPage] = useState(1)
  const [statusFilter, setStatusFilter] = useState<string>('ALL')
  const [typeFilter, setTypeFilter] = useState<string>('ALL')
  const [searchTerm, setSearchTerm] = useState('')
  const pageSize = 10

  // 获取订单列表
  const {
    data: ordersData,
    isLoading: ordersLoading,
    refetch,
  } = trpc.order.list.useQuery({
    page,
    pageSize,
    status: statusFilter === 'ALL' ? undefined : (statusFilter as 'PENDING' | 'COMPLETED' | 'CANCELLED'),
    type: typeFilter === 'ALL' ? undefined : (typeFilter as 'PURCHASE' | 'GIFT'),
    search: searchTerm || undefined,
  })

  // 申请发票mutation
  const requestInvoiceMutation = trpc.order.requestInvoice.useMutation({
    onSuccess: () => {
      toast.success('发票申请成功，请联系客服处理')
      refetch()
    },
    onError: (error) => {
      toast.error(error.message)
    },
  })

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'PENDING':
        return (
          <Badge variant="outline" className="text-yellow-600 border-yellow-600">
            待付款
          </Badge>
        )
      case 'COMPLETED':
        return (
          <Badge variant="outline" className="text-green-600 border-green-600">
            已完成
          </Badge>
        )
      case 'CANCELLED':
        return (
          <Badge variant="outline" className="text-red-600 border-red-600">
            已取消
          </Badge>
        )
      default:
        return <Badge variant="outline">未知</Badge>
    }
  }

  const getTypeBadge = (type: string) => {
    switch (type) {
      case 'PURCHASE':
        return (
          <Badge variant="outline" className="text-blue-600 border-blue-600">
            购买
          </Badge>
        )
      case 'GIFT':
        return (
          <Badge variant="outline" className="text-purple-600 border-purple-600">
            赠送
          </Badge>
        )
      default:
        return <Badge variant="outline">未知</Badge>
    }
  }

  const handleInvoiceRequest = (orderId: string) => {
    requestInvoiceMutation.mutate({ orderId })
  }

  const resetFilters = () => {
    setStatusFilter('ALL')
    setTypeFilter('ALL')
    setSearchTerm('')
    setPage(1)
  }

  // 使用 useEffect 来处理搜索词变化，实现实时搜索
  useEffect(() => {
    setPage(1)
  }, [searchTerm, statusFilter, typeFilter])

  if (ordersLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center gap-2">
        <ShoppingCart className="h-6 w-6" />
        <h1 className="text-2xl font-bold">订单管理</h1>
      </div>

      {/* 搜索和筛选 - 紧凑布局 */}
      <Card>
        <CardContent className="py-4">
          <div className="flex flex-wrap items-center gap-4">
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium text-muted-foreground">筛选：</span>
            </div>

            <div className="flex items-center gap-2">
              <Label htmlFor="search" className="text-sm text-muted-foreground whitespace-nowrap">
                订单号
              </Label>
              <Input
                id="search"
                placeholder="输入订单号搜索"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-40"
              />
            </div>

            <div className="flex items-center gap-2">
              <Label htmlFor="status" className="text-sm text-muted-foreground whitespace-nowrap">
                状态
              </Label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ALL">全部状态</SelectItem>
                  <SelectItem value="PENDING">待付款</SelectItem>
                  <SelectItem value="COMPLETED">已完成</SelectItem>
                  <SelectItem value="CANCELLED">已取消</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center gap-2">
              <Label htmlFor="type" className="text-sm text-muted-foreground whitespace-nowrap">
                类型
              </Label>
              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ALL">全部类型</SelectItem>
                  <SelectItem value="PURCHASE">购买</SelectItem>
                  <SelectItem value="GIFT">赠送</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Button variant="outline" size="sm" onClick={resetFilters}>
              重置
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 订单列表 */}
      <Card>
        <CardHeader>
          <CardTitle>订单列表</CardTitle>
          <CardDescription>查看您的蚁贝充值和购买记录</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>订单号</TableHead>
                <TableHead>类型</TableHead>
                <TableHead>蚁贝数量</TableHead>
                <TableHead>金额</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>创建时间</TableHead>
                <TableHead>操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {ordersData?.data && ordersData.data.length > 0 ? (
                ordersData.data.map((order) => (
                  <TableRow key={order.id}>
                    <TableCell className="font-mono text-sm">{order.orderNo}</TableCell>
                    <TableCell>{getTypeBadge(order.type)}</TableCell>
                    <TableCell>
                      <span className="font-medium">{parseFloat(order.antCoins).toFixed(2)} 蚁贝</span>
                    </TableCell>
                    <TableCell>
                      <span className="font-medium">¥{parseFloat(order.amount).toFixed(2)}</span>
                    </TableCell>
                    <TableCell>{getStatusBadge(order.status)}</TableCell>
                    <TableCell>{new Date(order.createdAt).toLocaleString('zh-CN')}</TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        {order.type === 'PURCHASE' && order.status === 'COMPLETED' && !order.invoiceRequested && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleInvoiceRequest(order.id)}
                            disabled={requestInvoiceMutation.isPending}
                          >
                            <Receipt className="h-4 w-4 mr-1" />
                            申请发票
                          </Button>
                        )}
                        {order.invoiceRequested && (
                          <Badge variant="outline" className="text-green-600 border-green-600">
                            已申请发票
                          </Badge>
                        )}
                        {order.remarks && (
                          <Dialog>
                            <DialogTrigger asChild>
                              <Button variant="outline" size="sm">
                                查看备注
                              </Button>
                            </DialogTrigger>
                            <DialogContent>
                              <DialogHeader>
                                <DialogTitle>订单备注</DialogTitle>
                                <DialogDescription>订单号：{order.orderNo}</DialogDescription>
                              </DialogHeader>
                              <div className="mt-4">
                                <p className="text-sm text-muted-foreground">{order.remarks}</p>
                              </div>
                            </DialogContent>
                          </Dialog>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={7} className="text-center text-muted-foreground">
                    暂无订单记录
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>

          {/* 分页 */}
          {ordersData && ordersData.data.length > 0 && (
            <div className="flex items-center justify-between mt-4">
              <div className="text-sm text-muted-foreground">
                共 {ordersData.total} 条记录，第 {page} 页
              </div>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm" disabled={page <= 1} onClick={() => setPage(page - 1)}>
                  上一页
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  disabled={!ordersData.data || ordersData.data.length < pageSize}
                  onClick={() => setPage(page + 1)}
                >
                  下一页
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
