# 测试指南

## 概述

本项目使用 Vitest 作为测试框架，专注于 Service 层的单元测试，确保业务逻辑的正确性和可靠性。

## 测试架构

### 测试策略

- **单元测试**: 主要针对 Service 层，使用 mock Repository 依赖
- **集成测试**: 测试 API 路由和中间件
- **端到端测试**: 测试完整的用户流程

### 测试覆盖范围

- ✅ Service 层业务逻辑
- ✅ 工具类和辅助函数
- 🔄 API 路由（计划中）
- 🔄 中间件（计划中）

## 运行测试

### 基本命令

```bash
# 运行所有测试
pnpm test

# 监听模式运行测试
pnpm test:watch

# 运行测试并生成覆盖率报告
pnpm test:coverage
```

### 针对特定包运行测试

```bash
# 只运行 server-core 包的测试
pnpm -F @my-org/server-core test

# 运行特定测试文件
pnpm -F @my-org/server-core test crypto.service.test.ts
```

## 编写测试

### Service 层测试示例

```typescript
import { describe, it, expect, beforeEach, vi } from 'vitest'
import { UserService } from '../user.service'
import type { UserRepository } from '../../../db/repositories/user.repository'

// Mock Repository
const createMockUserRepository = (): UserRepository =>
  ({
    findById: vi.fn(),
    findByEmail: vi.fn(),
    create: vi.fn(),
    // ... 其他方法
  }) as any

describe('UserService', () => {
  let userService: UserService
  let mockUserRepository: UserRepository

  beforeEach(() => {
    mockUserRepository = createMockUserRepository()
    userService = new UserService(mockUserRepository, mockAuthService)
  })

  it('should return user without password', async () => {
    // 测试逻辑
  })
})
```

### 测试最佳实践

1. **使用描述性的测试名称**: 清楚地描述测试的目的
2. **遵循 AAA 模式**: Arrange（准备）、Act（执行）、Assert（断言）
3. **Mock 外部依赖**: 使用 vi.fn() 创建 mock 函数
4. **测试边界情况**: 包括错误情况和边界值
5. **保持测试独立**: 每个测试应该独立运行

## CI/CD 集成

### GitHub Actions

项目配置了 GitHub Actions 工作流，在每次推送和 PR 时自动运行：

- 类型检查
- 代码格式检查
- 单元测试
- 构建验证
- 测试覆盖率报告

### Pre-commit Hooks

使用 Husky 配置了 pre-commit hooks：

- 类型检查
- 代码格式检查
- 单元测试
- 提交消息格式验证

## 测试覆盖率

目标测试覆盖率：

- **Service 层**: 90%+
- **工具类**: 95%+
- **整体项目**: 80%+

查看覆盖率报告：

```bash
pnpm test:coverage
```

覆盖率报告会生成在 `packages/server-core/coverage/` 目录下。

## 故障排除

### 常见问题

1. **Mock 不生效**: 确保在 `beforeEach` 中重置 mock
2. **异步测试失败**: 使用 `async/await` 或返回 Promise
3. **类型错误**: 确保 mock 对象包含所需的方法

### 调试测试

```bash
# 运行特定测试并显示详细输出
pnpm -F @my-org/server-core test --reporter=verbose crypto.service.test.ts

# 在监听模式下运行特定测试
pnpm -F @my-org/server-core test --watch crypto.service.test.ts
```

## 贡献指南

在提交代码前，请确保：

1. 所有测试通过
2. 新功能包含相应的测试
3. 测试覆盖率不降低
4. 遵循现有的测试模式和约定
