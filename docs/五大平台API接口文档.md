# 五大社交媒体平台 API 接口文档

## 概述

本文档整理了 qdy-service 项目中对接的五大社交媒体平台的开放API接口，为业务迁移提供完整的技术参考。

## 平台列表

- 🎵 **抖音开放平台** - 功能最完整的主要平台
- 📹 **微信视频号** - 重要平台，通过第三方API服务
- ⚡ **快手开放平台** - 短视频和直播平台
- 📱 **微博开放平台** - 社交媒体平台
- 📝 **小红书开放平台** - 生活分享平台

---

## 1. 抖音开放平台 API

### 基础信息

- **官方文档**: https://developer.open-douyin.com/
- **API基础地址**: `https://open.douyin.com`

### 1.1 认证授权相关

#### 获取访问令牌

- **接口地址**: `POST https://open.douyin.com/oauth/access_token/`
- **功能描述**: 通过授权码获取访问令牌
- **请求参数**:
  ```json
  {
    "client_key": "应用ID",
    "client_secret": "应用密钥",
    "code": "授权码",
    "grant_type": "authorization_code"
  }
  ```
- **返回字段**:
  ```json
  {
    "access_token": "访问令牌",
    "open_id": "用户唯一标识",
    "refresh_token": "刷新令牌",
    "expires_in": "过期时间(秒)",
    "refresh_expires_in": "刷新令牌过期时间(秒)"
  }
  ```

#### 获取用户信息

- **接口地址**: `POST https://open.douyin.com/oauth/userinfo/`
- **功能描述**: 获取授权用户的基本信息
- **请求参数**:
  ```json
  {
    "access_token": "访问令牌",
    "open_id": "用户唯一标识"
  }
  ```
- **返回字段**:
  ```json
  {
    "avatar": "头像URL",
    "nickname": "昵称",
    "open_id": "用户唯一标识",
    "e_account_role": "账号角色"
  }
  ```

#### 刷新访问令牌

- **接口地址**: `POST https://open.douyin.com/oauth/refresh_token/`
- **功能描述**: 刷新过期的访问令牌
- **请求参数**:
  ```json
  {
    "client_key": "应用ID",
    "refresh_token": "刷新令牌",
    "grant_type": "refresh_token"
  }
  ```

#### 获取客户端令牌

- **接口地址**: `POST https://open.douyin.com/oauth/client_token/`
- **功能描述**: 获取应用级别的客户端令牌
- **请求参数**:
  ```json
  {
    "client_key": "应用ID",
    "client_secret": "应用密钥",
    "grant_type": "client_credential"
  }
  ```

#### 角色检查

- **接口地址**: `POST https://open.douyin.com/api/douyin/v1/role/check/`
- **功能描述**: 检查用户账号角色（企业、认证等）
- **请求参数**:
  ```json
  {
    "open_id": "用户唯一标识",
    "role_labels": ["COMPANY_BAND", "AUTH_COMPANY", "STAFF"]
  }
  ```

### 1.2 私信消息相关

#### 发送私信

- **接口地址**: `POST https://open.douyin.com/im/send/msg/`
- **功能描述**: 向指定用户发送私信
- **请求参数**:
  ```json
  {
    "msg_id": "消息ID",
    "conversation_id": "会话ID",
    "to_user_id": "接收用户ID",
    "content": {
      "msg_type": 1, // 1:文本 2:图片 8:卡片
      "text": { "text": "消息内容" }
    },
    "scene": "im_replay_msg"
  }
  ```
- **请求头**: `access-token: {访问令牌}`

#### 发送群消息

- **接口地址**: `POST https://open.douyin.com/im/send/msg/group/`
- **功能描述**: 向粉丝群发送消息
- **请求参数**:
  ```json
  {
    "group_id": "群组ID",
    "content": {
      "msg_type": 1,
      "text": { "text": "消息内容" }
    }
  }
  ```

#### 撤回消息

- **接口地址**: `POST https://open.douyin.com/im/recall/msg/`
- **功能描述**: 撤回已发送的私信或群消息
- **请求参数**:
  ```json
  {
    "conversation_id": "会话ID",
    "conversation_type": 1, // 1:私信 2:群聊
    "msg_id": "消息ID"
  }
  ```

### 1.3 评论管理相关

#### 回复评论

- **接口地址**: `POST https://open.douyin.com/item/comment/reply/`
- **功能描述**: 回复视频评论
- **请求参数**:
  ```json
  {
    "item_id": "视频ID",
    "comment_id": "评论ID",
    "content": "回复内容"
  }
  ```
- **URL参数**: `?open_id={用户ID}&device_brand={设备品牌}`

#### 置顶评论

- **接口地址**: `POST https://open.douyin.com/item/comment/top/`
- **功能描述**: 置顶或取消置顶评论
- **请求参数**:
  ```json
  {
    "item_id": "视频ID",
    "comment_id": "评论ID",
    "top": true // true:置顶 false:取消置顶
  }
  ```

#### 获取评论列表

- **接口地址**: `GET https://open.douyin.com/item/comment/list/`
- **功能描述**: 获取视频评论列表
- **URL参数**:
  - `open_id`: 用户ID
  - `item_id`: 视频ID
  - `cursor`: 分页游标
  - `count`: 返回数量
  - `sort_type`: 排序类型

### 1.4 视频内容相关

#### 获取视频列表

- **接口地址**: `GET https://open.douyin.com/api/douyin/v1/video/video_list`
- **功能描述**: 获取用户发布的视频列表
- **URL参数**:
  - `open_id`: 用户ID
  - `cursor`: 分页游标
  - `count`: 返回数量

#### 获取视频详情

- **接口地址**: `POST https://open.douyin.com/api/douyin/v1/video/video_data`
- **功能描述**: 获取指定视频的详细信息
- **请求参数**:
  ```json
  {
    "item_ids": ["视频ID1", "视频ID2"],
    "video_ids": ["video_id1", "video_id2"]
  }
  ```

### 1.5 群组管理相关

#### 创建粉丝群

- **接口地址**: `POST https://open.douyin.com/im/group/fans/create/`
- **功能描述**: 创建粉丝群
- **请求参数**:
  ```json
  {
    "group_name": "群名称",
    "description": "群描述"
  }
  ```

#### 获取粉丝群列表

- **接口地址**: `GET https://open.douyin.com/im/group/fans/list/`
- **功能描述**: 获取用户的粉丝群列表
- **URL参数**: `open_id={用户ID}`

#### 设置入群审核

- **接口地址**: `POST https://open.douyin.com/im/group/enter/audit/set/`
- **功能描述**: 审核用户加群申请
- **请求参数**:
  ```json
  {
    "apply_id": "申请ID",
    "status": 1 // 1:通过 2:拒绝
  }
  ```

#### 设置群欢迎语

- **接口地址**: `POST https://open.douyin.com/im/group/setting/set/`
- **功能描述**: 设置群欢迎语
- **请求参数**:
  ```json
  {
    "group_id": "群ID",
    "group_setting_type": 1,
    "msg_list": [
      {
        "msg_type": 1,
        "text": { "text": "欢迎语内容" }
      }
    ]
  }
  ```

#### 禁用群配置

- **接口地址**: `POST https://open.douyin.com/im/group/setting/disable/`
- **功能描述**: 禁用群欢迎语等配置
- **请求参数**:
  ```json
  {
    "group_id": "群ID",
    "group_setting_type": 1
  }
  ```

### 1.6 留资卡片相关

#### 保存留资卡片

- **接口地址**: `POST https://open.douyin.com/im/save/retain_consult_card/`
- **功能描述**: 创建或编辑留资卡片
- **请求参数**:
  ```json
  {
    "card_id": "卡片ID",
    "components": [1, 2, 3], // 组件类型
    "media_id": "媒体ID",
    "title": "卡片标题"
  }
  ```

#### 获取留资卡片

- **接口地址**: `GET https://open.douyin.com/im/get/retain_consult_card/`
- **功能描述**: 查询用户的留资卡片列表
- **URL参数**: `open_id={用户ID}`

#### 删除留资卡片

- **接口地址**: `GET https://open.douyin.com/im/del/retain_consult_card/`
- **功能描述**: 删除指定的留资卡片
- **URL参数**:
  - `open_id`: 用户ID
  - `card_id`: 卡片ID

### 1.7 媒体上传相关

#### 上传图片

- **接口地址**: `POST https://open.douyin.com/tool/imagex/client_upload/`
- **功能描述**: 上传图片文件
- **请求类型**: multipart/form-data
- **请求参数**: 图片文件流

---

## 2. 微信视频号 API

### 基础信息

- **服务商**: 第三方API服务
- **API基础地址**:
  - 旧版: `http://api.videosapi.com/finder/v2`
  - 新版: `http://**************:4408/finder/v2`

### 2.1 登录认证相关

#### 获取登录二维码

- **接口地址**: `POST /api/login/getLoginQrCode`
- **功能描述**: 获取微信扫码登录二维码
- **请求参数**:
  ```json
  {
    "appId": "设备ID",
    "regionId": "代理地区ID",
    "useProxy": true
  }
  ```
- **返回字段**:
  ```json
  {
    "qrData": "二维码数据",
    "qrImgBase64": "二维码图片Base64",
    "uuid": "登录UUID",
    "appId": "设备ID"
  }
  ```

#### 检查登录状态

- **接口地址**: `POST /api/login/checkLogin`
- **功能描述**: 检查二维码扫描登录状态
- **请求参数**:
  ```json
  {
    "appId": "设备ID",
    "uuid": "登录UUID",
    "captchCode": "验证码",
    "useProxy": true
  }
  ```
- **返回字段**:
  ```json
  {
    "loginInfo": {
      "wxid": "微信ID",
      "nickName": "昵称",
      "mobile": "手机号"
    },
    "status": 1
  }
  ```

#### 登出

- **接口地址**: `POST /api/login/logout`
- **功能描述**: 退出登录
- **请求参数**:
  ```json
  {
    "appId": "设备ID",
    "useProxy": true
  }
  ```

#### 检查在线状态

- **接口地址**: `POST /api/login/checkOnline`
- **功能描述**: 检查账号在线状态
- **请求参数**:
  ```json
  {
    "appId": "设备ID"
  }
  ```

#### 重连

- **接口地址**: `POST /api/login/reconnection`
- **功能描述**: 重新连接断开的账号
- **请求参数**:
  ```json
  {
    "appId": "设备ID"
  }
  ```

### 2.2 用户信息相关

#### 获取用户资料

- **接口地址**: `POST /api/finder/getProfile`
- **功能描述**: 获取视频号用户资料信息
- **请求参数**:
  ```json
  {
    "appId": "设备ID",
    "useProxy": true
  }
  ```
- **返回字段**:
  ```json
  {
    "mainFinderUsername": "视频号用户名",
    "aliasInfo": [
      {
        "nickname": "昵称",
        "headImgUrl": "头像URL",
        "roleType": 3
      }
    ]
  }
  ```

#### 获取联系人列表

- **接口地址**: `POST /api/finder/contactList`
- **功能描述**: 获取联系人列表
- **请求参数**:
  ```json
  {
    "appId": "设备ID",
    "myUserName": "我的用户名",
    "myRoleType": 3,
    "queryInfo": "查询信息"
  }
  ```

### 2.3 内容交互相关

#### 评论操作

- **接口地址**: `POST /api/finder/comment`
- **功能描述**: 发表或删除评论
- **请求参数**:
  ```json
  {
    "appId": "设备ID",
    "myRoleType": 3,
    "myUserName": "用户名",
    "objectId": "视频ID",
    "content": "评论内容",
    "opType": 0, // 0:发表 1:删除
    "sessionBuffer": "会话缓冲",
    "useProxy": true
  }
  ```

#### 获取评论列表

- **接口地址**: `POST /api/finder/commentList`
- **功能描述**: 获取视频评论列表
- **请求参数**:
  ```json
  {
    "appId": "设备ID",
    "objectId": "视频ID",
    "lastBuffer": "分页缓冲",
    "sessionBuffer": "会话缓冲"
  }
  ```

#### 获取用户主页

- **接口地址**: `POST /api/finder/userPage`
- **功能描述**: 获取用户主页视频列表
- **请求参数**:
  ```json
  {
    "appId": "设备ID",
    "toUserName": "目标用户名",
    "lastBuffer": "分页缓冲",
    "maxId": "最大ID",
    "useProxy": false
  }
  ```

#### 获取@列表

- **接口地址**: `POST /api/finder/mentionList`
- **功能描述**: 获取@我的消息列表
- **请求参数**:
  ```json
  {
    "appId": "设备ID",
    "myUserName": "用户名",
    "myRoleType": 3,
    "reqScene": 1,
    "lastBuff": "分页缓冲"
  }
  ```

### 2.4 消息相关

#### 获取消息会话ID

- **接口地址**: `POST /api/finder/getMsgSessionId`
- **功能描述**: 获取私信会话ID
- **请求参数**:
  ```json
  {
    "appId": "设备ID",
    "toUserName": "目标用户名",
    "myUserName": "我的用户名"
  }
  ```

#### 下载图片

- **接口地址**: `POST /api/message/downloadImage`
- **功能描述**: 下载消息中的图片
- **请求参数**:
  ```json
  {
    "appId": "设备ID",
    "xml": "图片XML数据",
    "type": 1
  }
  ```

#### 下载视频

- **接口地址**: `POST /api/message/downloadVideo`
- **功能描述**: 下载消息中的视频
- **请求参数**:
  ```json
  {
    "appId": "设备ID",
    "xml": "视频XML数据"
  }
  ```

---

## 3. 快手开放平台 API

### 基础信息

- **官方文档**: https://open.kuaishou.com/
- **API基础地址**: `https://open.kuaishou.com`

### 3.1 认证授权相关

#### 获取访问令牌

- **接口地址**: `GET https://open.kuaishou.com/oauth2/access_token`
- **功能描述**: 通过授权码获取访问令牌
- **请求参数**:
  - `app_id`: 应用ID
  - `app_secret`: 应用密钥
  - `code`: 授权码
  - `grant_type`: authorization_code
- **返回字段**:
  ```json
  {
    "result": 1,
    "access_token": "访问令牌",
    "open_id": "用户唯一标识",
    "refresh_token": "刷新令牌",
    "expires_in": "过期时间",
    "refresh_token_expires_in": "刷新令牌过期时间"
  }
  ```

#### 刷新访问令牌

- **接口地址**: `GET https://open.kuaishou.com/oauth2/refresh_token`
- **功能描述**: 刷新过期的访问令牌
- **请求参数**:
  - `app_id`: 应用ID
  - `app_secret`: 应用密钥
  - `refresh_token`: 刷新令牌
  - `grant_type`: refresh_token

### 3.2 用户信息相关

#### 获取用户信息

- **接口地址**: `GET https://open.kuaishou.com/openapi/user_info`
- **功能描述**: 获取授权用户的基本信息
- **请求参数**:
  - `access_token`: 访问令牌
  - `app_id`: 应用ID
- **返回字段**:
  ```json
  {
    "result": 1,
    "user_info": {
      "head": "头像URL",
      "name": "用户名",
      "open_id": "用户唯一标识",
      "fan": "粉丝数",
      "follow": "关注数"
    }
  }
  ```

### 3.3 消息发送相关

#### 发送消息

- **接口地址**: `POST https://open.kuaishou.com/openapi/message/send`
- **功能描述**: 发送私信消息
- **请求参数**:
  ```json
  {
    "kpn": "KUAISHOU",
    "sub_biz": "KSIM_TO_ADIM",
    "from_user": {
      "userId": "发送者ID",
      "userRole": 1
    },
    "to_user": {
      "userId": "接收者ID",
      "userRole": 2
    },
    "content": {
      "contentType": 1, // 1:文本 2:图片 10000:卡片
      "content": "{\"text\":\"消息内容\"}"
    },
    "scene": "im_replay_msg",
    "request_id": "请求ID"
  }
  ```
- **URL参数**:
  - `app_id`: 应用ID
  - `access_token`: 访问令牌

### 3.4 营销工具相关

#### 获取营销工具列表

- **接口地址**: `POST https://open.kuaishou.com/openapi/kuailiao/getMarketToolList`
- **功能描述**: 获取快手营销工具（留资卡片）列表
- **请求参数**:
  ```json
  {
    "open_id": "用户ID",
    "request_id": "请求ID"
  }
  ```
- **URL参数**:
  - `app_id`: 应用ID
  - `access_token`: 访问令牌

---

## 4. 微博开放平台 API

### 基础信息

- **官方文档**: https://open.weibo.com/
- **API基础地址**: `https://api.weibo.com`

### 4.1 认证授权相关

#### 获取访问令牌

- **接口地址**: `POST https://api.weibo.com/oauth2/access_token`
- **功能描述**: 通过授权码获取访问令牌
- **请求参数**:
  - `client_id`: 应用ID
  - `client_secret`: 应用密钥
  - `code`: 授权码
  - `grant_type`: authorization_code
  - `redirect_uri`: 回调地址
- **返回字段**:
  ```json
  {
    "access_token": "访问令牌",
    "uid": "用户ID",
    "expires_in": "过期时间"
  }
  ```

#### 获取令牌信息

- **接口地址**: `POST https://api.weibo.com/oauth2/get_token_info`
- **功能描述**: 获取访问令牌的详细信息
- **请求参数**:
  - `access_token`: 访问令牌

#### 撤销授权

- **接口地址**: `POST https://api.weibo.com/oauth2/revokeoauth2`
- **功能描述**: 撤销用户授权
- **请求参数**:
  - `access_token`: 访问令牌

### 4.2 用户信息相关

#### 获取用户信息

- **接口地址**: `GET https://api.weibo.com/2/users/show.json`
- **功能描述**: 获取用户基本信息
- **请求参数**:
  - `uid`: 用户ID
  - `access_token`: 访问令牌
- **返回字段**:
  ```json
  {
    "id": "用户ID",
    "screen_name": "用户名",
    "name": "昵称",
    "profile_image_url": "头像URL",
    "followers_count": "粉丝数",
    "friends_count": "关注数"
  }
  ```

---

## 5. 小红书开放平台 API

### 基础信息

- **官方文档**: https://ad.xiaohongshu.com/
- **API基础地址**: `https://adapi.xiaohongshu.com`

### 5.1 认证授权相关

#### 获取访问令牌

- **接口地址**: `POST https://adapi.xiaohongshu.com/api/open/oauth2/access_token`
- **功能描述**: 通过授权码获取访问令牌
- **请求参数**:
  ```json
  {
    "app_id": "应用ID",
    "secret": "应用密钥",
    "auth_code": "授权码"
  }
  ```
- **返回字段**:
  ```json
  {
    "success": true,
    "data": {
      "user_id": "用户ID",
      "access_token": "访问令牌",
      "refresh_token": "刷新令牌",
      "access_token_expires_in": "过期时间",
      "refresh_token_expires_in": "刷新令牌过期时间",
      "scope": "授权范围"
    }
  }
  ```

#### 刷新访问令牌

- **接口地址**: `POST https://adapi.xiaohongshu.com/api/open/oauth2/refresh_token`
- **功能描述**: 刷新过期的访问令牌
- **请求参数**:
  ```json
  {
    "app_id": "应用ID",
    "secret": "应用密钥",
    "refresh_token": "刷新令牌"
  }
  ```

### 5.2 消息相关

#### 发送消息

- **接口地址**: `POST https://adapi.xiaohongshu.com/api/open/im/third/send`
- **功能描述**: 发送私信消息
- **请求参数**:
  ```json
  {
    "user_id": "发送者ID",
    "receive_user_id": "接收者ID",
    "content_type": "TEXT", // TEXT/IMAGE/CARD/BUSINESSCARD
    "content": "加密后的消息内容",
    "client_msg_id": "客户端消息ID"
  }
  ```
- **请求头**: `Access-Token: {访问令牌}`

### 5.3 评论相关

#### 获取评论列表

- **接口地址**: `POST https://adapi.xiaohongshu.com/api/open/im/comment/list`
- **功能描述**: 获取笔记评论列表
- **请求参数**:
  ```json
  {
    "user_id": "用户ID",
    "page_num": 1,
    "page_size": 20,
    "begin_time": "开始时间",
    "end_time": "结束时间"
  }
  ```
- **返回字段**:
  ```json
  {
    "success": true,
    "data": {
      "total": 100,
      "list": [
        {
          "note_id": "笔记ID",
          "comment_id": "评论ID",
          "comment_content": "评论内容",
          "comment_user_name": "评论用户名",
          "comment_time": 1234567890
        }
      ]
    }
  }
  ```

### 5.4 素材管理相关

#### 获取素材列表

- **接口地址**: `POST https://adapi.xiaohongshu.com/api/open/im/material/list`
- **功能描述**: 获取可用的素材列表
- **请求参数**:
  ```json
  {
    "user_id": "用户ID",
    "page_num": 1,
    "page_size": 20,
    "type": 1 // 素材类型
  }
  ```

### 5.5 用户管理相关

#### 获取绑定用户列表

- **接口地址**: `POST https://adapi.xiaohongshu.com/api/open/im/auth/bind_users`
- **功能描述**: 获取已绑定的用户列表
- **请求参数**:
  ```json
  {
    "user_id": "用户ID",
    "page_num": 1,
    "page_size": 20
  }
  ```

---

## 6. 数据加密说明

### 6.1 快手平台加密

- **加密算法**: AES-128-CBC
- **密钥格式**: Base64编码
- **数据格式**: `{IV}:{加密数据}`

### 6.2 小红书平台加密

- **加密算法**: AES-128-CBC
- **密钥格式**: Base64编码
- **数据格式**: `{IV}~split~{加密数据}`

---

## 7. 错误码说明

### 7.1 抖音平台

- `error_code: 0` - 成功
- `error_code: 非0` - 失败，查看description字段

### 7.2 快手平台

- `result: 1` - 成功
- `result: 非1` - 失败，查看error_msg字段

### 7.3 小红书平台

- `success: true` - 成功
- `success: false` - 失败，查看msg字段

### 7.4 微信视频号

- `ret: 200` - 成功
- `ret: 非200` - 失败，查看msg字段

---

## 8. 开发建议

### 8.1 统一错误处理

建议对各平台的API响应进行统一的错误处理，根据不同平台的错误码格式进行适配。

### 8.2 令牌管理

- 实现访问令牌的自动刷新机制
- 建立令牌过期监控和告警
- 实现令牌的安全存储

### 8.3 接口限流

- 各平台都有接口调用频率限制
- 建议实现请求队列和限流机制
- 监控API调用量，避免超出限制

### 8.4 数据安全

- 敏感数据传输使用HTTPS
- API密钥安全存储，不要硬编码
- 实现请求签名验证

---

## 9. 业务迁移优先级建议

1. **第一优先级**: 抖音开放平台 - 功能最完整，用户量最大
2. **第二优先级**: 微信视频号 - 重要平台，商业价值高
3. **第三优先级**: 小红书开放平台 - 新兴平台，增长迅速
4. **第四优先级**: 快手开放平台 - 补充短视频平台
5. **第五优先级**: 微博开放平台 - 传统社交媒体平台

---

_文档版本: v1.0_  
_更新时间: 2024年12月_  
_维护者: qdy-service 团队_
