# OpenAPI Meta 参数文档

## 概述

本文档详细说明了 OpenAPI 认证相关接口的 Meta 参数信息，包括输入（Input）和输出（Output）的完整规范。

## 基础信息

- **Base URL**: `http://localhost:3000/api`
- **认证方式**: Bearer <PERSON>ken
- **内容类型**: `application/json`

## 接口列表

### 1. 获取访问令牌 (authenticate)

**基本信息**

- **路径**: `POST /auth/authenticate`
- **标签**: OpenAPI认证
- **摘要**: 获取访问令牌
- **描述**: 使用应用ID和密钥获取访问令牌，用于后续OpenAPI调用的身份验证。令牌有效期为30天。

**输入参数 (Input)**

```typescript
interface AuthenticateInput {
  appId: string // 应用唯一标识符，格式：app_xxxxxxxx
  secret: string // 应用密钥，格式：sk_xxxxxxxx
}
```

**Zod Schema**:

```typescript
const AuthenticateSchema = z.object({
  appId: z.string().min(1, 'App ID 不能为空').describe('应用唯一标识符，格式：app_xxxxxxxx'),
  secret: z.string().min(1, 'Secret 不能为空').describe('应用密钥，格式：sk_xxxxxxxx'),
})
```

**输出参数 (Output)**

```typescript
interface AuthenticateOutput {
  access_token: string // 访问令牌，用于后续API调用的身份验证
  token_type: string // 令牌类型，固定值：Bearer
  expires_in: number // 令牌过期时间，单位：秒
  application: {
    id: string // 应用数据库ID
    name: string // 应用名称
    appId: string // 应用唯一标识符
  }
  user: {
    id: string // 用户数据库ID
    name: string // 用户名称
  }
}
```

**示例请求**

```bash
curl -X POST http://localhost:3000/api/auth/authenticate \
  -H "Content-Type: application/json" \
  -d '{
    "appId": "app_1a2b3c4d5e6f7g8h",
    "secret": "sk_1a2b3c4d5e6f7g8h9i0j1k2l3m4n5o6p"
  }'
```

**示例响应 (200)**

```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "Bearer",
  "expires_in": 2592000,
  "application": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "name": "我的应用",
    "appId": "app_1a2b3c4d5e6f7g8h"
  },
  "user": {
    "id": "123e4567-e89b-12d3-a456-426614174001",
    "name": "张三"
  }
}
```

**错误响应 (401)**

```json
{
  "error": {
    "code": "UNAUTHORIZED",
    "message": "无效的 App ID"
  }
}
```

---

### 2. 验证访问令牌 (verify)

**基本信息**

- **路径**: `GET /auth/verify`
- **标签**: OpenAPI认证
- **摘要**: 验证访问令牌
- **描述**: 验证当前访问令牌是否有效，并返回关联的用户和应用信息。
- **认证**: 需要 Bearer Token

**输入参数 (Input)**

```typescript
// 无需输入参数，通过 Authorization Header 传递 Token
type VerifyInput = void
```

**输出参数 (Output)**

```typescript
interface VerifyOutput {
  valid: boolean // Token是否有效
  user: {
    id: string // 用户数据库ID
    name: string // 用户名称
    phone?: string // 用户手机号（可选）
  }
  application: {
    id: string // 应用数据库ID
    name: string // 应用名称
    appId: string // 应用唯一标识符
  }
}
```

**示例请求**

```bash
curl -X GET http://localhost:3000/api/auth/verify \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

**示例响应 (200)**

```json
{
  "valid": true,
  "user": {
    "id": "123e4567-e89b-12d3-a456-426614174001",
    "name": "张三",
    "phone": "13800138000"
  },
  "application": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "name": "我的应用",
    "appId": "app_1a2b3c4d5e6f7g8h"
  }
}
```

---

### 3. 获取应用信息 (getAppInfo)

**基本信息**

- **路径**: `GET /auth/app-info`
- **标签**: OpenAPI认证
- **摘要**: 获取应用信息
- **描述**: 获取当前访问令牌关联的应用详细信息，包括应用配置、流量使用情况等。
- **认证**: 需要 Bearer Token

**输入参数 (Input)**

```typescript
// 无需输入参数，通过 Authorization Header 传递 Token
type GetAppInfoInput = void
```

**输出参数 (Output)**

```typescript
interface GetAppInfoOutput {
  application: {
    id: string // 应用数据库ID
    name: string // 应用名称
    description?: string // 应用描述（可选）
    appId: string // 应用唯一标识符
    traffic: number // 流量使用量
    createdAt: string // 创建时间
  }
  user: {
    id: string // 用户数据库ID
    name: string // 用户名称
  }
}
```

**示例请求**

```bash
curl -X GET http://localhost:3000/api/auth/app-info \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

**示例响应 (200)**

```json
{
  "application": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "name": "我的应用",
    "description": "这是一个测试应用",
    "appId": "app_1a2b3c4d5e6f7g8h",
    "traffic": 1024,
    "createdAt": "2024-01-15T08:30:00.000Z"
  },
  "user": {
    "id": "123e4567-e89b-12d3-a456-426614174001",
    "name": "张三"
  }
}
```

## Meta 配置示例

以下是每个接口的 tRPC meta 配置示例：

### authenticate 接口

```typescript
.meta({
  openapi: {
    method: 'POST',
    path: '/auth/authenticate',
    tags: ['OpenAPI认证'],
    summary: '获取访问令牌',
    description: '使用应用ID和密钥获取访问令牌，用于后续OpenAPI调用的身份验证。令牌有效期为30天。',
  },
})
```

### verify 接口

```typescript
.meta({
  openapi: {
    method: 'GET',
    path: '/auth/verify',
    tags: ['OpenAPI认证'],
    summary: '验证访问令牌',
    description: '验证当前访问令牌是否有效，并返回关联的用户和应用信息。',
  },
})
```

### getAppInfo 接口

```typescript
.meta({
  openapi: {
    method: 'GET',
    path: '/auth/app-info',
    tags: ['OpenAPI认证'],
    summary: '获取应用信息',
    description: '获取当前访问令牌关联的应用详细信息，包括应用配置、流量使用情况等。',
  },
})
```

## 错误处理

所有接口都遵循统一的错误响应格式：

```typescript
interface ErrorResponse {
  error: {
    code: string // 错误代码
    message: string // 错误消息
  }
}
```

**常见错误码**:

- `UNAUTHORIZED`: 认证失败，Token无效或已过期
- `FORBIDDEN`: 权限不足
- `NOT_FOUND`: 资源不存在
- `BAD_REQUEST`: 请求参数错误
- `INTERNAL_SERVER_ERROR`: 服务器内部错误

## 认证流程

1. **获取令牌**: 使用 `appId` 和 `secret` 调用 `/auth/authenticate` 接口获取访问令牌
2. **使用令牌**: 在后续 API 调用中，在 `Authorization` 头部添加 `Bearer {access_token}`
3. **验证令牌**: 可调用 `/auth/verify` 接口验证令牌是否有效
4. **获取信息**: 使用 `/auth/app-info` 接口获取应用详细信息

## 注意事项

1. **令牌有效期**: 访问令牌有效期为30天（2,592,000秒）
2. **密钥安全**: `secret` 仅在应用创建时显示一次，请妥善保管
3. **请求频率**: 建议合理控制API请求频率，避免过度调用
4. **HTTPS**: 生产环境请使用 HTTPS 协议确保通信安全
