{"version": "2.0.0", "tasks": [{"label": "📦 安装依赖", "type": "shell", "command": "pnpm", "args": ["install"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "🚀 启动服务端", "type": "shell", "command": "pnpm", "args": ["dev:server"], "group": "build", "isBackground": true, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "problemMatcher": {"pattern": {"regexp": "^(.*):(\\d+):(\\d+): (error|warning): (.*)$", "file": 1, "line": 2, "column": 3, "severity": 4, "message": 5}, "background": {"activeOnStart": true, "beginsPattern": "^.*服务器启动中.*$", "endsPattern": "^.*服务器启动成功.*$"}}}, {"label": "🌐 启动客户端", "type": "shell", "command": "pnpm", "args": ["dev:client"], "group": "build", "isBackground": true, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "problemMatcher": {"pattern": {"regexp": "^(.*):(\\d+):(\\d+): (error|warning): (.*)$", "file": 1, "line": 2, "column": 3, "severity": 4, "message": 5}, "background": {"activeOnStart": true, "beginsPattern": "^.*Local:.*$", "endsPattern": "^.*ready in.*$"}}}, {"label": "🔥 启动全栈开发", "dependsOrder": "parallel", "dependsOn": ["🚀 启动服务端", "🌐 启动客户端"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}}, {"label": "🏗️ 构建服务端", "type": "shell", "command": "pnpm", "args": ["build:server"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$tsc"]}, {"label": "🏗️ 构建客户端", "type": "shell", "command": "pnpm", "args": ["build:client"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$tsc"]}, {"label": "🏗️ 构建全部", "type": "shell", "command": "pnpm", "args": ["build"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$tsc"]}, {"label": "🧪 运行 E2E 测试", "type": "shell", "command": "pnpm", "args": ["test"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "dedicated", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "🔍 Lint 服务端", "type": "shell", "command": "pnpm", "args": ["-F", "server", "lint"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$eslint-stylish"]}, {"label": "🔍 Lint 客户端", "type": "shell", "command": "pnpm", "args": ["-F", "client", "lint"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$eslint-stylish"]}, {"label": "🗄️ 数据库推送", "type": "shell", "command": "pnpm", "args": ["push"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "🎛️ 数据库管理界面", "type": "shell", "command": "pnpm", "args": ["studio"], "group": "build", "isBackground": true, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "🧹 清理依赖", "type": "shell", "command": "pnpm", "args": ["clean"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}]}